<?php

namespace Database\Seeders\Auth;

use App\Events\Backend\UserCreated;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Schema;

/**
 * Class UserTableSeeder.
 */
class UserTableSeeder extends Seeder
{
    /**
     * Run the database seed.
     *
     * @return void
     */
    public function run()
    {
        Schema::disableForeignKeyConstraints();

        $faker = \Faker\Factory::create();

        // Add the master administrator, user id of 1
        $users = [
            [
                'id' => 1,
                'first_name' => 'Super',
                'last_name' => 'Admin',
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('secret'),
                'username' => '100001',
                'mobile' => $faker->phoneNumber,
                'date_of_birth' => $faker->date,
                'avatar' => 'img/default-avatar.jpg',
                'gender' => $faker->randomElement(['Male', 'Female', 'Other']),
                'email_verified_at' => Carbon::now(),
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'id' => 2,
                'first_name' => 'Admin',
                'last_name' => 'Istrator',
                'name' => 'Admin Istrator',
                'email' => '<EMAIL>',
                'password' => Hash::make('secret'),
                'username' => '100002',
                'mobile' => $faker->phoneNumber,
                'date_of_birth' => $faker->date,
                'avatar' => 'img/default-avatar.jpg',
                'gender' => $faker->randomElement(['Male', 'Female', 'Other']),
                'email_verified_at' => Carbon::now(),
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'id' => 3,
                'first_name' => 'Manager',
                'last_name' => 'User User',
                'name' => 'Manager',
                'email' => '<EMAIL>',
                'password' => Hash::make('secret'),
                'username' => '100003',
                'mobile' => $faker->phoneNumber,
                'date_of_birth' => $faker->date,
                'avatar' => 'img/default-avatar.jpg',
                'gender' => $faker->randomElement(['Male', 'Female', 'Other']),
                'email_verified_at' => Carbon::now(),
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'id' => 4,
                'first_name' => 'Executive',
                'last_name' => 'User',
                'name' => 'Executive User',
                'email' => '<EMAIL>',
                'password' => Hash::make('secret'),
                'username' => '100004',
                'mobile' => $faker->phoneNumber,
                'date_of_birth' => $faker->date,
                'avatar' => 'img/default-avatar.jpg',
                'gender' => $faker->randomElement(['Male', 'Female', 'Other']),
                'email_verified_at' => Carbon::now(),
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'id' => 5,
                'first_name' => 'General',
                'last_name' => 'User',
                'name' => 'General User',
                'email' => '<EMAIL>',
                'password' => Hash::make('secret'),
                'username' => '100005',
                'mobile' => $faker->phoneNumber,
                'date_of_birth' => $faker->date,
                'avatar' => 'img/default-avatar.jpg',
                'gender' => $faker->randomElement(['Male', 'Female', 'Other']),
                'email_verified_at' => Carbon::now(),
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
        ];

        foreach ($users as $user_data) {
            $user = User::create($user_data);

            event(new UserCreated($user));
        }

        Schema::enableForeignKeyConstraints();
    }
}
