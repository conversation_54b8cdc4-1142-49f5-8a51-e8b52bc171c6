[2025-08-15 14:09:52] local.INFO: Cart Debug - Raw values {"amt_raw":"223534.08","amt_vat_raw":"44706.82","amt_cleaned":223534.08,"amt_vat_cleaned":44706.82,"unit_price_tra":88.2}
[2025-08-15 14:09:52] local.INFO: Cart Debug - Calculated values {"amt_tra":223534.08,"amt":223534.08,"amt_vat_tra":44706.82,"amt_vat":44706.82,"cur_rate_tra":1}
[2025-08-15 14:09:57] local.DEBUG: switching mode {"is_test_mode":false}
[2025-08-15 14:10:02] local.INFO: <PERSON><PERSON><PERSON><PERSON><PERSON> | Kullanıcı:<PERSON>(ID:1)
[2025-08-15 14:10:06] local.DEBUG: switching mode {"is_test_mode":false}
[2025-08-15 14:10:06] local.DEBUG: switching mode {"is_test_mode":false}
[2025-08-15 14:10:06] local.INFO: Card type detection input {"card_prefix":"526911"}
[2025-08-15 14:10:06] local.INFO: Detected MasterCard card
[2025-08-15 14:10:06] local.INFO: Card type detection {"card_number_prefix":"5269","provided_card_type":"MasterCard","detected_card_type":"master","final_card_type":"master","pos_class":"Mews\\Pos\\Gateways\\KuveytPos","supported_card_types":["visa","master","troy"]}
[2025-08-15 14:10:06] local.DEBUG: preparing 3D form data
[2025-08-15 14:10:06] local.DEBUG: sending request {"url":"https://sanalpos.kuveytturk.com.tr/ServiceGateWay/Home/ThreeDModelPayGate"}
[2025-08-15 14:10:07] local.DEBUG: request completed {"status_code":200}
[2025-08-15 14:10:22] local.DEBUG: switching mode {"is_test_mode":false}
[2025-08-15 14:10:22] local.DEBUG: switching mode {"is_test_mode":false}
[2025-08-15 14:10:22] local.INFO: Bank response received {"method":"POST","pos_class":"Mews\\Pos\\Gateways\\KuveytPos","all_data":{"AuthenticationResponse":"%3c%3fxml+version%3d%221.0%22+encoding%3d%22utf-8%22%3f%3e%*********************************+xmlns%3axsd%3d%22http%3a%2f%2fwww.w3.org%2f2001%2fXMLSchema%22+xmlns%3axsi%3d%22http%3a%2f%2fwww.w3.org%2f2001%2fXMLSchema-instance%22%3e%3cVPosMessage%3e%3cOrderId%3e284087859%3c%2fOrderId%3e%3cOkUrl%3ehttps%3a%2f%2fportal.akdagtasyunu.com%2fpayment%2f3d%2fresponse%3c%2fOkUrl%3e%3cFailUrl%3ehttps%3a%2f%2fportal.akdagtasyunu.com%2fpayment%2f3d%2fresponse%3c%2fFailUrl%3e%3cMerchantId%3e588693%3c%2fMerchantId%3e%3cSubMerchantId%3e0%3c%2fSubMerchantId%3e%3cCustomerId%3e98267952%3c%2fCustomerId%3e%3cUserName+%2f%3e%3cHashPassword+%2f%3e%3cCardNumber%3e52691102****2095%3c%2fCardNumber%3e%3cIsTemporaryCard+xsi%3anil%3d%22true%22+%2f%3e%3cBatchID%3e1%3c%2fBatchID%3e%3cInstallmentCount%3e0%3c%2fInstallmentCount%3e%3cAmount%3e2000%3c%2fAmount%3e%3cCancelAmount%3e0%3c%2fCancelAmount%3e%3cMerchantOrderId%3eP598%3c%2fMerchantOrderId%3e%3cOrderStatus+xsi%3anil%3d%22true%22+%2f%3e%3cRetryCount+xsi%3anil%3d%22true%22+%2f%3e%3cFECAmount%3e0%3c%2fFECAmount%3e%3cCurrencyCode%3e949%3c%2fCurrencyCode%3e%3cQeryId%3e0%3c%2fQeryId%3e%3cDebtId%3e0%3c%2fDebtId%3e%3cSurchargeAmount%3e0%3c%2fSurchargeAmount%3e%3cSGKDebtAmount%3e0%3c%2fSGKDebtAmount%3e%3cTokenInfoId+xsi%3anil%3d%22true%22+%2f%3e%3cTransactionSecurity%3e3%3c%2fTransactionSecurity%3e%3cDeferringCount+xsi%3anil%3d%22true%22+%2f%3e%3cInstallmentMaturityCommisionFlag%3e0%3c%2fInstallmentMaturityCommisionFlag%3e%3cPaymentId+xsi%3anil%3d%22true%22+%2f%3e%3cParentPaymentId+xsi%3anil%3d%22true%22+%2f%3e%3cOrderPOSTransactionId+xsi%3anil%3d%22true%22+%2f%3e%3cTranDate+xsi%3anil%3d%22true%22+%2f%3e%3cTransactionUserId+xsi%3anil%3d%22true%22+%2f%3e%3cDeviceData+xsi%3anil%3d%22true%22+%2f%3e%3cCardHolderData+xsi%3anil%3d%22true%22+%2f%3e%3c%2fVPosMessage%3e%3cIsEnrolled%3etrue%3c%2fIsEnrolled%3e%3cIsVirtual%3efalse%3c%2fIsVirtual%3e%3cResponseCode%3e00%3c%2fResponseCode%3e%3cResponseMessage%3eKart+do%c4%9fruland%c4%b1.%3c%2fResponseMessage%3e%3cOrderId%3e284087859%3c%2fOrderId%3e%3cTransactionTime%3e0001-01-01T00%3a00%3a00%3c%2fTransactionTime%3e%3cMerchantOrderId%3eP598%3c%2fMerchantOrderId%3e%3cHashData%3el7FQ6eK9oQAtRf9SHlN3ChrvLbs%3d%3c%2fHashData%3e%3cMD%3ezmo82BwiLwHlqpLXCYs8iXCJKrXJdBUGJxNs9%2bsCCkf3Lo5MuQWZcJ1yAYlNMgAt%3c%2fMD%3e%3cMerchantId+xsi%3anil%3d%22true%22+%2f%3e%3cBusinessKey%3e202508159552000000000032381%3c%2fBusinessKey%3e%3cMDStatus%3e%3cIsSelected%3efalse%3c%2fIsSelected%3e%3cIsSelectable%3etrue%3c%2fIsSelectable%3e%3cMDStatusCode%3e1%3c%2fMDStatusCode%3e%3cMDStatusDescription%3eAUTHENTICATION_SUCCESSFUL%3c%2fMDStatusDescription%3e%3c%2fMDStatus%3e%3c%2fVPosTransactionResponseContract%3e"},"raw_post":"AuthenticationResponse=%253c%253fxml%2Bversion%253d%25221.0%2522%2Bencoding%253d%2522utf-8%2522%253f%253e%25*********************************%2Bxmlns%253axsd%253d%2522http%253a%252f%252fwww.w3.org%252f2001%252fXMLSchema%2522%2Bxmlns%253axsi%253d%2522http%253a%252f%252fwww.w3.org%252f2001%252fXMLSchema-instance%2522%253e%253cVPosMessage%253e%253cOrderId%253e284087859%253c%252fOrderId%253e%253cOkUrl%253ehttps%253a%252f%252fportal.akdagtasyunu.com%252fpayment%252f3d%252fresponse%253c%252fOkUrl%253e%253cFailUrl%253ehttps%253a%252f%252fportal.akdagtasyunu.com%252fpayment%252f3d%252fresponse%253c%252fFailUrl%253e%253cMerchantId%253e588693%253c%252fMerchantId%253e%253cSubMerchantId%253e0%253c%252fSubMerchantId%253e%253cCustomerId%253e98267952%253c%252fCustomerId%253e%253cUserName%2B%252f%253e%253cHashPassword%2B%252f%253e%253cCardNumber%253e52691102****2095%253c%252fCardNumber%253e%253cIsTemporaryCard%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cBatchID%253e1%253c%252fBatchID%253e%253cInstallmentCount%253e0%253c%252fInstallmentCount%253e%253cAmount%253e2000%253c%252fAmount%253e%253cCancelAmount%253e0%253c%252fCancelAmount%253e%253cMerchantOrderId%253eP598%253c%252fMerchantOrderId%253e%253cOrderStatus%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cRetryCount%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cFECAmount%253e0%253c%252fFECAmount%253e%253cCurrencyCode%253e949%253c%252fCurrencyCode%253e%253cQeryId%253e0%253c%252fQeryId%253e%253cDebtId%253e0%253c%252fDebtId%253e%253cSurchargeAmount%253e0%253c%252fSurchargeAmount%253e%253cSGKDebtAmount%253e0%253c%252fSGKDebtAmount%253e%253cTokenInfoId%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cTransactionSecurity%253e3%253c%252fTransactionSecurity%253e%253cDeferringCount%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cInstallmentMaturityCommisionFlag%253e0%253c%252fInstallmentMaturityCommisionFlag%253e%253cPaymentId%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cParentPaymentId%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cOrderPOSTransactionId%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cTranDate%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cTransactionUserId%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cDeviceData%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cCardHolderData%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253c%252fVPosMessage%253e%253cIsEnrolled%253etrue%253c%252fIsEnrolled%253e%253cIsVirtual%253efalse%253c%252fIsVirtual%253e%253cResponseCode%253e00%253c%252fResponseCode%253e%253cResponseMessage%253eKart%2Bdo%25c4%259fruland%25c4%25b1.%253c%252fResponseMessage%253e%253cOrderId%253e284087859%253c%252fOrderId%253e%253cTransactionTime%253e0001-01-01T00%253a00%253a00%253c%252fTransactionTime%253e%253cMerchantOrderId%253eP598%253c%252fMerchantOrderId%253e%253cHashData%253el7FQ6eK9oQAtRf9SHlN3ChrvLbs%253d%253c%252fHashData%253e%253cMD%253ezmo82BwiLwHlqpLXCYs8iXCJKrXJdBUGJxNs9%252bsCCkf3Lo5MuQWZcJ1yAYlNMgAt%253c%252fMD%253e%253cMerchantId%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cBusinessKey%253e202508159552000000000032381%253c%252fBusinessKey%253e%253cMDStatus%253e%253cIsSelected%253efalse%253c%252fIsSelected%253e%253cIsSelectable%253etrue%253c%252fIsSelectable%253e%253cMDStatusCode%253e1%253c%252fMDStatusCode%253e%253cMDStatusDescription%253eAUTHENTICATION_SUCCESSFUL%253c%252fMDStatusDescription%253e%253c%252fMDStatus%253e%253c%252fVPosTransactionResponseContract%253e","headers":{"content-length":["3423"],"content-type":["application/x-www-form-urlencoded"],"accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"accept-language":["en-US,en;q=0.9"],"cache-control":["no-cache"],"connection":["close"],"cookie":["XSRF-TOKEN=eyJpdiI6IjE4YThodDVqakVIWlA3RkN6TytVeFE9PSIsInZhbHVlIjoiNVpjcmVldmtyeGdKeG1taTAySlkwaHBJZkFlNExxRnJGTGk2QmI3MGs0SDljQlJCS2dXMm5mdEs3WkFRUFhOclRpQ0cyMzdGL2tyWFgxZFJscWRieGNnbGRiaEVPeDJmZXpDblJyampNb05CbUxrNVFEZ3cyQzFXWjl3aExwdHoiLCJtYWMiOiI5OGM0Mjk0YjQzYTMzN2E1YjJjMmRlMjA1MWIzMGQyN2JiYTNjZGM1MzRiNjdjNDRiZWY5NTE0NzJmOWEzZWFkIiwidGFnIjoiIn0%3D; akdag_yalitim_session=eyJpdiI6IncrVlZwaUtndDdsVDVBZTJMOWZjdVE9PSIsInZhbHVlIjoidmFJeTF3ZkN4ejc4ZWo2bXNiOFJaOXhwejI3dW1UdGZFc0xjWnp4U2FqbzQzRU5RY2FSVUhiS2NLZHgzbXlGYUZkMm8vUFBNNlE5YXY5YU5BVEZFcE5xaThYSld6RWpNeSs4RXRjdkpGR3dWTW9MYmdJTDFuVUE5UWkwWnVXd2UiLCJtYWMiOiJhNTE3MGMwMzlhODM5NzVlM2EzM2JhYjlkYTA3YzQ4NWU1NDMzOGY5ZDg1ZmI4OWE2N2VlYzU0OTI3ZWY2ZjU4IiwidGFnIjoiIn0%3D"],"dnt":["1"],"host":["portal.akdagtasyunu.com"],"origin":["https://sanalpos.kuveytturk.com.tr"],"pragma":["no-cache"],"referer":["https://sanalpos.kuveytturk.com.tr/"],"sec-ch-ua":["\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\""],"sec-ch-ua-mobile":["?0"],"sec-ch-ua-platform":["\"Windows\""],"sec-fetch-dest":["document"],"sec-fetch-mode":["navigate"],"sec-fetch-site":["cross-site"],"upgrade-insecure-requests":["1"],"user-agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"x-forwarded-for":["************"],"x-forwarded-host":["portal.akdagtasyunu.com"],"x-forwarded-proto":["https"],"x-forwarded-server":["portal.akdagtasyunu.com"],"x-real-ip":["************"]}}
[2025-08-15 14:10:22] local.INFO: KuveytPos detected, trying mewebstudio/pos package first
[2025-08-15 14:10:22] local.DEBUG: payment called {"card_provided":false,"tx_type":"pay","model":"3d"}
[2025-08-15 14:10:22] local.INFO: 3d auth success {"md_status":"00"}
[2025-08-15 14:10:22] local.DEBUG: finishing payment
[2025-08-15 14:10:22] local.DEBUG: sending request {"url":"https://sanalpos.kuveytturk.com.tr/ServiceGateWay/Home/ThreeDModelPayGate/ThreeDModelProvisionGate"}
[2025-08-15 14:10:22] local.DEBUG: request completed {"status_code":200}
[2025-08-15 14:10:22] local.WARNING: KuveytPos mewebstudio/pos failed, falling back to manual processing {"error":"Attempt to read property \"attributes\" on null"}
[2025-08-15 14:10:22] local.INFO: Falling back to manual KuveytPos processing
[2025-08-15 14:10:22] local.INFO: KuveytPos full manual processing started {"request_method":"POST","request_data":{"AuthenticationResponse":"%3c%3fxml+version%3d%221.0%22+encoding%3d%22utf-8%22%3f%3e%*********************************+xmlns%3axsd%3d%22http%3a%2f%2fwww.w3.org%2f2001%2fXMLSchema%22+xmlns%3axsi%3d%22http%3a%2f%2fwww.w3.org%2f2001%2fXMLSchema-instance%22%3e%3cVPosMessage%3e%3cOrderId%3e284087859%3c%2fOrderId%3e%3cOkUrl%3ehttps%3a%2f%2fportal.akdagtasyunu.com%2fpayment%2f3d%2fresponse%3c%2fOkUrl%3e%3cFailUrl%3ehttps%3a%2f%2fportal.akdagtasyunu.com%2fpayment%2f3d%2fresponse%3c%2fFailUrl%3e%3cMerchantId%3e588693%3c%2fMerchantId%3e%3cSubMerchantId%3e0%3c%2fSubMerchantId%3e%3cCustomerId%3e98267952%3c%2fCustomerId%3e%3cUserName+%2f%3e%3cHashPassword+%2f%3e%3cCardNumber%3e52691102****2095%3c%2fCardNumber%3e%3cIsTemporaryCard+xsi%3anil%3d%22true%22+%2f%3e%3cBatchID%3e1%3c%2fBatchID%3e%3cInstallmentCount%3e0%3c%2fInstallmentCount%3e%3cAmount%3e2000%3c%2fAmount%3e%3cCancelAmount%3e0%3c%2fCancelAmount%3e%3cMerchantOrderId%3eP598%3c%2fMerchantOrderId%3e%3cOrderStatus+xsi%3anil%3d%22true%22+%2f%3e%3cRetryCount+xsi%3anil%3d%22true%22+%2f%3e%3cFECAmount%3e0%3c%2fFECAmount%3e%3cCurrencyCode%3e949%3c%2fCurrencyCode%3e%3cQeryId%3e0%3c%2fQeryId%3e%3cDebtId%3e0%3c%2fDebtId%3e%3cSurchargeAmount%3e0%3c%2fSurchargeAmount%3e%3cSGKDebtAmount%3e0%3c%2fSGKDebtAmount%3e%3cTokenInfoId+xsi%3anil%3d%22true%22+%2f%3e%3cTransactionSecurity%3e3%3c%2fTransactionSecurity%3e%3cDeferringCount+xsi%3anil%3d%22true%22+%2f%3e%3cInstallmentMaturityCommisionFlag%3e0%3c%2fInstallmentMaturityCommisionFlag%3e%3cPaymentId+xsi%3anil%3d%22true%22+%2f%3e%3cParentPaymentId+xsi%3anil%3d%22true%22+%2f%3e%3cOrderPOSTransactionId+xsi%3anil%3d%22true%22+%2f%3e%3cTranDate+xsi%3anil%3d%22true%22+%2f%3e%3cTransactionUserId+xsi%3anil%3d%22true%22+%2f%3e%3cDeviceData+xsi%3anil%3d%22true%22+%2f%3e%3cCardHolderData+xsi%3anil%3d%22true%22+%2f%3e%3c%2fVPosMessage%3e%3cIsEnrolled%3etrue%3c%2fIsEnrolled%3e%3cIsVirtual%3efalse%3c%2fIsVirtual%3e%3cResponseCode%3e00%3c%2fResponseCode%3e%3cResponseMessage%3eKart+do%c4%9fruland%c4%b1.%3c%2fResponseMessage%3e%3cOrderId%3e284087859%3c%2fOrderId%3e%3cTransactionTime%3e0001-01-01T00%3a00%3a00%3c%2fTransactionTime%3e%3cMerchantOrderId%3eP598%3c%2fMerchantOrderId%3e%3cHashData%3el7FQ6eK9oQAtRf9SHlN3ChrvLbs%3d%3c%2fHashData%3e%3cMD%3ezmo82BwiLwHlqpLXCYs8iXCJKrXJdBUGJxNs9%2bsCCkf3Lo5MuQWZcJ1yAYlNMgAt%3c%2fMD%3e%3cMerchantId+xsi%3anil%3d%22true%22+%2f%3e%3cBusinessKey%3e202508159552000000000032381%3c%2fBusinessKey%3e%3cMDStatus%3e%3cIsSelected%3efalse%3c%2fIsSelected%3e%3cIsSelectable%3etrue%3c%2fIsSelectable%3e%3cMDStatusCode%3e1%3c%2fMDStatusCode%3e%3cMDStatusDescription%3eAUTHENTICATION_SUCCESSFUL%3c%2fMDStatusDescription%3e%3c%2fMDStatus%3e%3c%2fVPosTransactionResponseContract%3e"},"request_content":"AuthenticationResponse=%253c%253fxml%2Bversion%253d%25221.0%2522%2Bencoding%253d%2522utf-8%2522%253f%253e%25*********************************%2Bxmlns%253axsd%253d%2522http%253a%252f%252fwww.w3.org%252f2001%252fXMLSchema%2522%2Bxmlns%253axsi%253d%2522http%253a%252f%252fwww.w3.org%252f2001%252fXMLSchema-instance%2522%253e%253cVPosMessage%253e%253cOrderId%253e284087859%253c%252fOrderId%253e%253cOkUrl%253ehttps%253a%252f%252fportal.akdagtasyunu.com%252fpayment%252f3d%252fresponse%253c%252fOkUrl%253e%253cFailUrl%253ehttps%253a%252f%252fportal.akdagtasyunu.com%252fpayment%252f3d%252fresponse%253c%252fFailUrl%253e%253cMerchantId%253e588693%253c%252fMerchantId%253e%253cSubMerchantId%253e0%253c%252fSubMerchantId%253e%253cCustomerId%253e98267952%253c%252fCustomerId%253e%253cUserName%2B%252f%253e%253cHashPassword%2B%252f%253e%253cCardNumber%253e52691102****2095%253c%252fCardNumber%253e%253cIsTemporaryCard%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cBatchID%253e1%253c%252fBatchID%253e%253cInstallmentCount%253e0%253c%252fInstallmentCount%253e%253cAmount%253e2000%253c%252fAmount%253e%253cCancelAmount%253e0%253c%252fCancelAmount%253e%253cMerchantOrderId%253eP598%253c%252fMerchantOrderId%253e%253cOrderStatus%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cRetryCount%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cFECAmount%253e0%253c%252fFECAmount%253e%253cCurrencyCode%253e949%253c%252fCurrencyCode%253e%253cQeryId%253e0%253c%252fQeryId%253e%253cDebtId%253e0%253c%252fDebtId%253e%253cSurchargeAmount%253e0%253c%252fSurchargeAmount%253e%253cSGKDebtAmount%253e0%253c%252fSGKDebtAmount%253e%253cTokenInfoId%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cTransactionSecurity%253e3%253c%252fTransactionSecurity%253e%253cDeferringCount%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cInstallmentMaturityCommisionFlag%253e0%253c%252fInstallmentMaturityCommisionFlag%253e%253cPaymentId%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cParentPaymentId%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cOrderPOSTransactionId%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cTranDate%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cTransactionUserId%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cDeviceData%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cCardHolderData%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253c%252fVPosMessage%253e%253cIsEnrolled%253etrue%253c%252fIsEnrolled%253e%253cIsVirtual%253efalse%253c%252fIsVirtual%253e%253cResponseCode%253e00%253c%252fResponseCode%253e%253cResponseMessage%253eKart%2Bdo%25c4%259fruland%25c4%25b1.%253c%252fResponseMessage%253e%253cOrderId%253e284087859%253c%252fOrderId%253e%253cTransactionTime%253e0001-01-01T00%253a00%253a00%253c%252fTransactionTime%253e%253cMerchantOrderId%253eP598%253c%252fMerchantOrderId%253e%253cHashData%253el7FQ6eK9oQAtRf9SHlN3ChrvLbs%253d%253c%252fHashData%253e%253cMD%253ezmo82BwiLwHlqpLXCYs8iXCJKrXJdBUGJxNs9%252bsCCkf3Lo5MuQWZcJ1yAYlNMgAt%253c%252fMD%253e%253cMerchantId%2Bxsi%253anil%253d%2522true%2522%2B%252f%253e%253cBusinessKey%253e202508159552000000000032381%253c%252fBusinessKey%253e%253cMDStatus%253e%253cIsSelected%253efalse%253c%252fIsSelected%253e%253cIsSelectable%253etrue%253c%252fIsSelectable%253e%253cMDStatusCode%253e1%253c%252fMDStatusCode%253e%253cMDStatusDescription%253eAUTHENTICATION_SUCCESSFUL%253c%252fMDStatusDescription%253e%253c%252fMDStatus%253e%253c%252fVPosTransactionResponseContract%253e"}
[2025-08-15 14:10:22] local.INFO: Processing AuthenticationResponse
[2025-08-15 14:10:22] local.INFO: KuveytPos XML parsed values {"response_code":"00","response_message":"Kart doğrulandı.","md_status_code":"1","md_status_description":"AUTHENTICATION_SUCCESSFUL","merchant_order_id":"P598","order_id":"284087859","amount":"2000","installment_count":"0","business_key":"202508159552000000000032381"}
[2025-08-15 14:10:22] local.INFO: KuveytPos MD and BusinessKey values {"business_key":"202508159552000000000032381","md_value":"zmo82BwiLwHlqpLXCYs8iXCJKrXJdBUGJxNs9+sCCkf3Lo5MuQWZcJ1yAYlNMgAt","md_length":64,"business_key_length":27,"md_is_base64":true,"business_key_is_base64":false}
[2025-08-15 14:10:22] local.INFO: Using MD value as BusinessKey (Base64 format) {"md_value":"zmo82BwiLwHlqpLXCYs8iXCJKrXJdBUGJxNs9+sCCkf3Lo5MuQWZcJ1yAYlNMgAt"}
[2025-08-15 14:10:22] local.INFO: 3D authentication successful, sending provision request
[2025-08-15 14:10:22] local.INFO: KuveytPos provision request starting {"order_id":"P598","amount":20.0,"auth_response":{"status":"approved","error_message":"","md_error_message":"","installment_count":0,"ref_ret_num":"zmo82BwiLwHlqpLXCYs8iXCJKrXJdBUGJxNs9+sCCkf3Lo5MuQWZcJ1yAYlNMgAt","order_id":"284087859","merchant_order_id":"P598","amount":"2000","response_code":"00","response_message":"Kart doğrulandı.","md_status":"1","md_status_description":"AUTHENTICATION_SUCCESSFUL"}}
[2025-08-15 14:10:22] local.INFO: KuveytPos provision data prepared {"provision_data":{"MerchantId":"588693","CustomerId":"98267952","UserName":"kwebservis","Password":"3GgejhjaHCSX55k","OrderId":"284087859","MerchantOrderId":"P598","Amount":"2000","InstallmentCount":0,"BusinessKey":"zmo82BwiLwHlqpLXCYs8iXCJKrXJdBUGJxNs9+sCCkf3Lo5MuQWZcJ1yAYlNMgAt","TransactionType":"Sale"},"auth_response_amount":"2000","order_amount":20.0}
[2025-08-15 14:10:22] local.INFO: KuveytPos provision hash calculated (mewebstudio/pos method) {"merchant_id":"588693","merchant_order_id":"P598","amount":"2000","username":"kwebservis","password":"***masked***","hashed_password":"7UmtBkEwy8yh7ZzLASn+05dTLvw=","hash_string":"588693P5982000kwebservis7UmtBkEwy8yh7ZzLASn+05dTLvw=","final_hash":"TK745fg35LpIhndCO0GoDQWX9pk=","hash_length":28}
[2025-08-15 14:10:22] local.INFO: KuveytPos provision XML built (official format) {"xml_length":726,"api_version":"TDV2.0.0","merchant_id":"588693","customer_id":"98267952","amount":"2000","business_key":"zmo82BwiLwHlqpLXCYs8iXCJKrXJdBUGJxNs9+sCCkf3Lo5MuQWZcJ1yAYlNMgAt","original_business_key":"zmo82BwiLwHlqpLXCYs8iXCJKrXJdBUGJxNs9+sCCkf3Lo5MuQWZcJ1yAYlNMgAt","business_key_valid":true,"hash_data":"TK745fg35LpIhndCO0GoDQWX9pk="}
[2025-08-15 14:10:22] local.INFO: KuveytPos provision XML request {"xml":"<?xml version=\"1.0\" encoding=\"utf-8\"?><KuveytTurkVPosMessage xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\"><APIVersion>TDV2.0.0</APIVersion><HashData>TK745fg35LpIhndCO0GoDQWX9pk=</HashData><MerchantId>588693</MerchantId><CustomerId>98267952</CustomerId><UserName>kwebservis</UserName><TransactionType>Sale</TransactionType><InstallmentCount>0</InstallmentCount><Amount>2000</Amount><MerchantOrderId>P598</MerchantOrderId><TransactionSecurity>3</TransactionSecurity><KuveytTurkVPosAdditionalData><AdditionalData><Key>MD</Key><Data>zmo82BwiLwHlqpLXCYs8iXCJKrXJdBUGJxNs9+sCCkf3Lo5MuQWZcJ1yAYlNMgAt</Data></AdditionalData></KuveytTurkVPosAdditionalData></KuveytTurkVPosMessage>"}
[2025-08-15 14:10:22] local.INFO: KuveytPos sending provision request {"url":"https://sanalpos.kuveytturk.com.tr/ServiceGateWay/Home/ThreeDModelProvisionGate","xml_request":"<?xml version=\"1.0\" encoding=\"utf-8\"?><KuveytTurkVPosMessage xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\"><APIVersion>TDV2.0.0</APIVersion><HashData>TK745fg35LpIhndCO0GoDQWX9pk=</HashData><MerchantId>588693</MerchantId><CustomerId>98267952</CustomerId><UserName>kwebservis</UserName><TransactionType>Sale</TransactionType><InstallmentCount>0</InstallmentCount><Amount>2000</Amount><MerchantOrderId>P598</MerchantOrderId><TransactionSecurity>3</TransactionSecurity><KuveytTurkVPosAdditionalData><AdditionalData><Key>MD</Key><Data>zmo82BwiLwHlqpLXCYs8iXCJKrXJdBUGJxNs9+sCCkf3Lo5MuQWZcJ1yAYlNMgAt</Data></AdditionalData></KuveytTurkVPosAdditionalData></KuveytTurkVPosMessage>"}
[2025-08-15 14:10:23] local.INFO: KuveytPos provision response received {"status_code":200,"headers":{"Cache-Control":["private"],"Content-Type":["application/xml; charset=utf-8"],"Date":["Fri, 15 Aug 2025 11:10:22 GMT"],"Content-Length":["1849"],"Strict-Transport-Security":["max-age=16070400; includeSubDomains; preload"],"Set-Cookie":["TS010f2491=0176dcd71c99944cc9d9bd5d2c82c173b2afcd2b238d665498cb582880d5b4519e0a6a3d8ab3f42711f64468c08834433903d39639; Path=/; Domain=.sanalpos.kuveytturk.com.tr; Secure; Httponly;"]},"response":"<?xml version=\"1.0\" encoding=\"utf-8\"?><VPosTransactionResponseContract xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"><VPosMessage><OrderId>284087859</OrderId><OkUrl>https://portal.akdagtasyunu.com/payment/3d/response</OkUrl><FailUrl>https://portal.akdagtasyunu.com/payment/3d/response</FailUrl><MerchantId>588693</MerchantId><SubMerchantId>0</SubMerchantId><CustomerId>98267952</CustomerId><UserName /><HashPassword /><CardNumber>52691102****2095</CardNumber><IsTemporaryCard xsi:nil=\"true\" /><BatchID>1</BatchID><InstallmentCount>0</InstallmentCount><Amount>2000</Amount><CancelAmount>0</CancelAmount><MerchantOrderId>P598</MerchantOrderId><OrderStatus xsi:nil=\"true\" /><RetryCount xsi:nil=\"true\" /><FECAmount>0</FECAmount><CurrencyCode>949</CurrencyCode><QeryId>0</QeryId><DebtId>0</DebtId><SurchargeAmount>0</SurchargeAmount><SGKDebtAmount>0</SGKDebtAmount><TokenInfoId xsi:nil=\"true\" /><TransactionSecurity>3</TransactionSecurity><DeferringCount xsi:nil=\"true\" /><InstallmentMaturityCommisionFlag>0</InstallmentMaturityCommisionFlag><PaymentId xsi:nil=\"true\" /><ParentPaymentId xsi:nil=\"true\" /><OrderPOSTransactionId xsi:nil=\"true\" /><TranDate xsi:nil=\"true\" /><TransactionUserId xsi:nil=\"true\" /><DeviceData xsi:nil=\"true\" /><CardHolderData xsi:nil=\"true\" /></VPosMessage><IsEnrolled>true</IsEnrolled><IsVirtual>false</IsVirtual><ProvisionNumber>518986</ProvisionNumber><RRN>522714407756</RRN><Stan>407756</Stan><ResponseCode>00</ResponseCode><ResponseMessage>OTORİZASYON VERİLDİ</ResponseMessage><OrderId>284087859</OrderId><TransactionTime>2025-08-15T14:10:06.87</TransactionTime><MerchantOrderId>P598</MerchantOrderId><HashData>Y2qybSrCYapDASxeRCsVJamEcw4=</HashData><MerchantId xsi:nil=\"true\" /><BusinessKey>202508159551000000000047547</BusinessKey></VPosTransactionResponseContract>"}
[2025-08-15 14:10:23] local.INFO: KuveytPos provision response parsed {"response_code":"00","response_message":"OTORİZASYON VERİLDİ","order_id":"284087859","merchant_order_id":"P598","amount":"2000","provision_number":"518986","rrn":"522714407756","stan":"407756","installment_count":"0"}
[2025-08-15 14:10:23] local.INFO: KuveytPos provision successful {"response_code":"00","provision_number":"518986","rrn":"522714407756"}
[2025-08-15 14:10:23] local.INFO: KuveytPos payment success check {"response_status":"approved","pos_is_success":false,"response_code":"00","final_success":true,"response_data":{"status":"approved","error_message":"","md_error_message":"","installment_count":0,"ref_ret_num":"518986","order_id":"284087859","merchant_order_id":"P598","amount":"2000","response_code":"00","response_message":"OTORİZASYON VERİLDİ","provision_number":"518986","rrn":"522714407756","stan":"407756"}}
[2025-08-15 14:10:30] local.ERROR: Cannot use object of type GuzzleHttp\Cookie\CookieJar as array {"exception":"[object] (Error(code: 0): Cannot use object of type GuzzleHttp\\Cookie\\CookieJar as array at /home/<USER>/portal/app/Console/Commands/InvoicesCommand.php:41)
[stacktrace]
#0 /home/<USER>/portal/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Console\\Commands\\InvoicesCommand->handle()
#1 /home/<USER>/portal/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 /home/<USER>/portal/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#3 /home/<USER>/portal/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#4 /home/<USER>/portal/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call()
#5 /home/<USER>/portal/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()
#6 /home/<USER>/portal/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()
#7 /home/<USER>/portal/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#8 /home/<USER>/portal/vendor/symfony/console/Application.php(1094): Illuminate\\Console\\Command->run()
#9 /home/<USER>/portal/vendor/symfony/console/Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#10 /home/<USER>/portal/vendor/symfony/console/Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#11 /home/<USER>/portal/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#12 /home/<USER>/portal/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#13 {main}
"}
