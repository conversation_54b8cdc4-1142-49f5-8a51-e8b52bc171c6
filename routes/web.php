<?php

use App\Http\Controllers\Frontend\AddressController;
use App\Http\Controllers\Frontend\AjaxController;
use App\Http\Controllers\Frontend\CalendarController;
use App\Http\Controllers\Frontend\CartController;
use App\Http\Controllers\Frontend\ChequeRecordController;
use App\Http\Controllers\Frontend\ContractController;
use App\Http\Controllers\Frontend\EntityController;
use App\Http\Controllers\Frontend\HomeController;
use App\Http\Controllers\Frontend\InvoiceController;
use App\Http\Controllers\Frontend\LetterCreditController;
use App\Http\Controllers\Frontend\OfferController;
use App\Http\Controllers\Frontend\OrderController;
use App\Http\Controllers\Frontend\PageController;
use App\Http\Controllers\Frontend\PaymentController;
use App\Http\Controllers\Frontend\PosPaymentController;
use App\Http\Controllers\Frontend\PriceController;
use App\Http\Controllers\Frontend\ProductController;
use App\Http\Controllers\Frontend\SupportController;
use App\Http\Controllers\Frontend\WaybillController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\TestaController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Auth Routes
require __DIR__ . '/auth.php';


Route::group(['middleware' => ['auth', 'password.change']], function () {
    Route::get('panel', [HomeController::class, 'home'])->name('home');

// Language Switch

    Route::get('cari', [EntityController::class, 'index']);
    Route::get('bakiye', [EntityController::class, 'balance']);
    Route::get('ekstre', [EntityController::class, 'ekstre']);
    Route::get('change_entity', [EntityController::class, 'change_entity']);
    Route::post('change_entity', [EntityController::class, 'change_entity_update']);
    Route::get('hakkimizda', [PageController::class, 'about']);
    Route::get('iletisim', [PageController::class, 'contact']);
    Route::post('iletisim', [PageController::class, 'contact_post']);
    Route::get('kullanim-kosullari', [PageController::class, 'terms']);
    Route::get('gizlilik', [PageController::class, 'privacy']);
    Route::get('yardim', [PageController::class, 'help']);
    Route::get('belgeler/{slug}', [PageController::class, 'page'])->name('page');
    Route::post('belgeler/{slug}', [PageController::class, 'pageUpdate'])->name('page_update');
    Route::get('duyuru', [PageController::class, 'announcements'])->name('announcements');

    Route::post('duyuru-ekle', [PageController::class, 'storeAnnouncement']);
    Route::get('testa', [TestaController::class, 'index']);
    Route::get('takvim', [CalendarController::class, 'index']);
    Route::post('takvim', [CalendarController::class, 'store']);

    Route::resource('teklif', OfferController::class)->only(['index', 'show']);
    Route::get('teklif/{id}/detay', [OfferController::class, 'detail'])->name('offer.detail');
    Route::resource('teminat-mektubu', LetterCreditController::class)->only(['index', 'show']);
    Route::resource('adresler', AddressController::class);
    Route::resource('sepet', CartController::class)->only(['index', 'destroy', 'create', 'store', 'edit']);
    Route::post('approve', [CartController::class, 'approve']);
    Route::get('get-cart-count', [CartController::class, 'getCartItemCount']);
    Route::resource('cek', ChequeRecordController::class);
    Route::resource('fiyat', PriceController::class)->only(['index', 'show']);
    Route::get('nakliye-fiyat', [PriceController::class, 'shipping_price']);
    Route::post('upload-shipping-image', [PriceController::class, 'uploadShippingImage']);

    Route::resource('sozlesme', ContractController::class)->only(['index', 'show', 'edit', 'update']);
    Route::resource('irsaliye', WaybillController::class)->only(['index', 'show']);
    Route::resource('fatura', InvoiceController::class)->only(['index', 'show']);
    Route::get('siparis/olustur', [OrderController::class, 'create_wizard'])->name('order.create_wizard');
    Route::get('siparis/sevk-raporu', [OrderController::class, 'shipment_report'])->name('order.shipment_report');
    Route::get('siparis/rapor', [OrderController::class, 'report'])->name('order.report');
    Route::get('siparis/{order_id}/detay', [OrderController::class, 'detail']);
    Route::resource('siparis', OrderController::class)->only(['create', 'index', 'show', 'store', 'update']);
    Route::resource('odeme', PaymentController::class)->only(['index', 'edit', 'store', 'update']);
    Route::post('odeme-sil', [PaymentController::class, 'destroy'])->name('payment.delete');
    Route::post('odeme-islemi-sil', [PaymentController::class, 'destroy_transaction'])->name('payment_transaction.delete');
    Route::get('odeme-islemi/{id}', [PaymentController::class, 'show_transaction'])->name('payment_transaction.show');
    Route::post('payment-refunded', [PaymentController::class, 'payment_refunded'])->name('payment_transaction.payment_refunded');
    Route::get('odeme-yap', [PosPaymentController::class, 'make_payment'])->name('payment.make_payment');

    Route::get('odeme/islemler', [PaymentController::class, 'transactions'])->name('payment.transactions');
    Route::get('odeme/pdf/{id}', [PaymentController::class, 'view_pdf']);
    Route::get('odeme/{id}', [PaymentController::class, 'show'])->name('payment.show');
    Route::get('uretim-sorgu', [ProductController::class, 'production_query']);
    Route::post('uretim-sorgu', [ProductController::class, 'production_query']);
    Route::post('uretim-sorgu-olustur', [ProductController::class, 'production_query_store']);
    Route::get('stok-karti', [ProductController::class, 'index']);

    Route::post('send-message-store', [OfferController::class, 'send_message_store']);
    Route::get('send-message', [OfferController::class, 'send_message']);

    Route::get('dil/{language}', [LanguageController::class, 'switch'])->name('language.switch');
    Route::get('dashboard', [HomeController::class, 'index'])->name('dashboard');
    Route::get('iletisim-bilgileri', [HomeController::class, 'contactInfos']);

    Route::get('get-products', [AjaxController::class, 'get_products']);
    Route::get('get-product', [AjaxController::class, 'get_product']);
    Route::get('get-product-price', [AjaxController::class, 'get_product_price']);
    Route::get('get-contract-summary', [AjaxController::class, 'get_contract_summary']);
    Route::get('get-dbs', [AjaxController::class, 'get_dbs']);
    Route::get('pay-with-dbs', [AjaxController::class, 'pay_with_dbs']);
    Route::post('pay-with-dbs', [PaymentController::class, 'pay_with_dbs']);
    Route::get('get-cities', [AjaxController::class, 'get_cities']);
    Route::get('get-towns', [AjaxController::class, 'get_towns']);
    Route::get('cart-content', [AjaxController::class, 'get_cart_content']);
    Route::post('cart-content', [AjaxController::class, 'post_cart_content']);
    Route::get('delete-cart', [AjaxController::class, 'delete_cart_alert']);
    Route::post('delete-cart', [AjaxController::class, 'delete_cart']);
    Route::post('pay-with-eft', [AjaxController::class, 'pay_with_eft']);
    Route::post('pay-with-contract', [AjaxController::class, 'pay_with_contract']);
    Route::get('get-shipping-price', [AjaxController::class, 'get_shipping_price']);

    Route::get('entity-search', [AjaxController::class, 'entity_search']);
    Route::get('order-search', [AjaxController::class, 'order_search']);
    Route::get('dark-mode', [AjaxController::class, 'dark_mode']);
    Route::get('compact-mode', [AjaxController::class, 'compact_mode']);
    Route::post('upload', [AjaxController::class, 'upload']);
    Route::get('run_artisan', [AjaxController::class, 'run_artisan']);
    Route::post('entity-update-status', [AjaxController::class, 'entity_update_status'])->name('entity_update_status');

});

/*
*
* Frontend Routes
*
* --------------------------------------------------------------------
*/
Route::get('/', 'App\Http\Controllers\Frontend\HomeController@index')->name('index');
Route::get('get-installment', [AjaxController::class, 'get_installment']);

Route::group(['namespace' => 'App\Http\Controllers\Frontend', 'as' => 'frontend.'], function () {

    Route::group(['middleware' => ['auth']], function () {
        Route::get('destek', [SupportController::class, 'index']);
        Route::get('yardim', [SupportController::class, 'help']);
        Route::get('panel/ekip', [SupportController::class, 'manage_team']);
        Route::get('profil/{id}', ['as' => "users.profile", 'uses' => "UserController@profile"]);
        Route::get('profil/{id}/duzenle', ['as' => "users.profileEdit", 'uses' => "UserController@profileEdit"]);
        Route::get('profil/{id}/bildirim', ['as' => "users.notification", 'uses' => "UserController@notification"]);
        Route::get('profil/{id}/aktiviteler', ['as' => "users.activity", 'uses' => "UserController@activity"]);
        Route::patch('profil/{id}/duzenle', ['as' => "users.profileUpdate", 'uses' => "UserController@profileUpdate"]);
        Route::get('profil/sifre-degistir/{id}', ['as' => "users.changePassword", 'uses' => "UserController@changePassword"]);
        Route::patch('profil/sifre-degistir/{id}', ['as' => "users.changePasswordUpdate", 'uses' => "UserController@changePasswordUpdate"]);
        Route::get("users/emailConfirmationResend/{id}", ['as' => "users.emailConfirmationResend", 'uses' => "UserController@emailConfirmationResend"]);
        Route::delete("users/userProviderDestroy", ['as' => "users.userProviderDestroy", 'uses' => "UserController@userProviderDestroy"]);
    });


    // Reports Routes
    Route::middleware(['auth'])->prefix('raporlar')->name('reports.')->group(function () {
        Route::get('/', function () {
            return view('frontend.reports.index');
        })->name('index');
        Route::get('/sales', [App\Http\Controllers\Frontend\Reports\ReportController::class, 'salesReport'])->name('sales');
        Route::get('/product-sales', [App\Http\Controllers\Frontend\Reports\ReportController::class, 'productSalesReport'])->name('product-sales');
        Route::get('/sales-rep-performance', [App\Http\Controllers\Frontend\Reports\SalesRepPerformanceController::class, 'index'])
            ->name('sales-rep-performance');
        Route::get('/monthly-sales', [App\Http\Controllers\Frontend\Reports\SalesPerformanceController::class, 'monthlySales'])
            ->name('monthly-sales');
        Route::get('/yearly-sales', [App\Http\Controllers\Frontend\Reports\SalesPerformanceController::class, 'yearlySales'])
            ->name('yearly-sales');
        // Product Performance routes
        Route::get('/product-performance', [App\Http\Controllers\Frontend\Reports\ProductPerformanceController::class, 'index'])
            ->name('product-performance');
        Route::get('/product-performance/top-selling', [App\Http\Controllers\Frontend\Reports\ProductPerformanceController::class, 'topSellingProducts'])
            ->name('product-performance.top-selling');
        Route::get('/product-performance/category-sales', [App\Http\Controllers\Frontend\Reports\ProductPerformanceController::class, 'categorySales'])
            ->name('product-performance.category-sales');
        // Customer Analysis Reports
        Route::get('/reports/valuable-customers', [App\Http\Controllers\Frontend\Reports\CustomerAnalysisController::class, 'valuableCustomers'])
            ->name('valuable-customers');
        Route::get('/reports/purchase-frequency', [App\Http\Controllers\Frontend\Reports\CustomerAnalysisController::class, 'purchaseFrequency'])
            ->name('purchase-frequency');
        // New financial report routes
        Route::get('/payment-methods', [App\Http\Controllers\Frontend\Reports\FinancialReportController::class, 'paymentMethodsAnalysis'])
            ->name('payment-methods');
        Route::get('/contract-usage', [App\Http\Controllers\Frontend\Reports\FinancialReportController::class, 'contractUsageReport'])
            ->name('contract-usage');
        // New operational reports
        Route::get('/order-status-summary', [App\Http\Controllers\Frontend\Reports\OperationalReportController::class, 'orderStatusSummary'])
            ->name('order-status-summary');
        Route::get('/shipment-performance', [App\Http\Controllers\Frontend\Reports\OperationalReportController::class, 'shipmentPerformance'])
            ->name('shipment-performance');
        // Yeni Dashboard KPI raporları için route'lar

            Route::get('/general-performance', [App\Http\Controllers\Frontend\Reports\DashboardReportController::class, 'generalPerformance'])
                ->name('general-performance');
            Route::get('/sales-conversion', [App\Http\Controllers\Frontend\Reports\DashboardReportController::class, 'salesConversion'])
                ->name('sales-conversion');

        Route::get('/ai', [App\Http\Controllers\Frontend\Reports\AIController::class, 'index'])->name('ai.query.form');
        Route::match(['GET', 'POST'], '/ai', [App\Http\Controllers\Frontend\Reports\AIController::class, 'processQuery'])->name('ai.query.process');

    });
});

# /routes/web.php
//Route::match(['POST'], '/payment/3d/form', [\App\Http\Controllers\Frontend\ThreeDSecurePaymentController::class, 'form']);
//Route::match(['GET','POST'], '/payment/3d/response', [\App\Http\Controllers\Frontend\ThreeDSecurePaymentController::class, 'response']);

Route::match(['POST'], '/payment/3d/form', [PosPaymentController::class, 'form']);
Route::match(['GET','POST'], '/payment/3d/response', [PosPaymentController::class, 'response']);

//Route::group(['prefix' => 'dosya-yoneticisi', 'middleware' => ['web', 'auth', 'can:view_backend']], function () {
//    \UniSharp\LaravelFilemanager\Lfm::routes();
//});

/*
*
* Backend Routes
* These routes need view-backend permission
* --------------------------------------------------------------------
*/
Route::group(['namespace' => 'App\Http\Controllers\Backend', 'prefix' => 'admin', 'as' => 'backend.', 'middleware' => ['auth', 'can:view_backend']], function () {
    /**
     * Backend Dashboard
     * Namespaces indicate folder structure.
     */
    Route::get('/', 'BackendController@index')->name('home');
    Route::get('dashboard', 'BackendController@index')->name('dashboard');

    /*
     *
     *  Settings Routes
     *
     * ---------------------------------------------------------------------
     */
    Route::group(['middleware' => ['permission:edit_settings']], function () {
        Route::get("ayarlar", "SettingController@index")->name("settings");
        Route::post("settings", "SettingController@store")->name("settings.store");
        Route::get("search-entities", "SettingController@searchEntities")->name("settings.search-entities");
    });

    /*
    *
    *  Notification Routes
    *
    * ---------------------------------------------------------------------
    */

    Route::get("notifications", ['as' => "notifications.index", 'uses' => "NotificationsController@index"]);
    Route::get("notifications/markAllAsRead", ['as' => "notifications.markAllAsRead", 'uses' => "NotificationsController@markAllAsRead"]);
    Route::delete("notifications/deleteAll", ['as' => "notifications.deleteAll", 'uses' => "NotificationsController@deleteAll"]);
    Route::get("notifications/{id}", ['as' => "notifications.show", 'uses' => "NotificationsController@show"]);

    /*
    *
    *  Backup Routes
    *
    * ---------------------------------------------------------------------
    */

    Route::get("backups", ['as' => "backups.index", 'uses' => "BackupController@index"]);
    Route::get("backups/create", ['as' => "backups.create", 'uses' => "BackupController@create"]);
    Route::get("backups/download/{file_name}", ['as' => "backups.download", 'uses' => "BackupController@download"]);
    Route::get("backups/delete/{file_name}", ['as' => "backups.delete", 'uses' => "BackupController@delete"]);

    /*
    *
    *  Roles Routes
    *
    * ---------------------------------------------------------------------
    */
    $module_name = 'roles';
    $controller_name = 'RolesController';
    Route::resource("{$module_name}", "{$controller_name}");

    /*
    *
    *  Users Routes
    *
    * ---------------------------------------------------------------------
    */
//
//    Route::get("users/profil/{id}", ['as' => "users.profile", 'uses' => "UserController@profile"]);
//    Route::get("users/profil/{id}/edit", ['as' => "users.profileEdit", 'uses' => "UserController@profileEdit"]);
//    Route::patch("users/profil/{id}/edit", ['as' => "users.profileUpdate", 'uses' => "UserController@profileUpdate"]);
//    Route::get("users/emailConfirmationResend/{id}", ['as' => "users.emailConfirmationResend", 'uses' => "UserController@emailConfirmationResend"]);
//    Route::delete("users/userProviderDestroy", ['as' => "users.userProviderDestroy", 'uses' => "UserController@userProviderDestroy"]);
//    Route::get("users/profil/changeProfilePassword/{id}", ['as' => "users.changeProfilePassword", 'uses' => "UserController@changeProfilePassword"]);
//    Route::patch("users/profil/changeProfilePassword/{id}", ['as' => "users.changeProfilePasswordUpdate", 'uses' => "UserController@changeProfilePasswordUpdate"]);
//    Route::get("users/changePassword/{id}", ['as' => "users.changePassword", 'uses' => "UserController@changePassword"]);
//    Route::patch("users/changePassword/{id}", ['as' => "users.changePasswordUpdate", 'uses' => "UserController@changePasswordUpdate"]);
//    Route::patch("users/entities/{id}", ['as' => "users.entity_user", 'uses' => "UserController@entity_user"]);
//    Route::get('profil/entities/{id}', ['as' => "users.entities", 'uses' => "UserController@entity_user"]);
//
//    Route::get("users/trashed", ['as' => "users.trashed", 'uses' => "UserController@trashed"]);
//    Route::patch("users/trashed/{id}", ['as' => "users.restore", 'uses' => "UserController@restore"]);
//    Route::get("users/index_data", ['as' => "users.index_data", 'uses' => "UserController@index_data"]);
//    Route::get("users/index_list", ['as' => "users.index_list", 'uses' => "UserController@index_list"]);
//    Route::resource("users", "UserController");
//    Route::patch("users/{id}/block", ['as' => "users.block", 'uses' => "UserController@block", 'middleware' => ['permission:block_users']]);
//    Route::patch("users/{id}/unblock", ['as' => "users.unblock", 'uses' => "UserController@unblock", 'middleware' => ['permission:block_users']]);


    // Kullanıcı rotaları
    Route::resource('users', 'App\Http\Controllers\Backend\UserController');
    Route::get('users/{id}/password', 'App\Http\Controllers\Backend\UserController@showPasswordForm')->name('users.password');
    Route::post('users/{id}/password', 'App\Http\Controllers\Backend\UserController@updatePassword')->name('users.update-password');
    Route::get('users/{id}/send-password', 'App\Http\Controllers\Backend\UserController@sendPasswordReset')->name('users.send-password');
    Route::post('users/{id}/status', 'App\Http\Controllers\Backend\UserController@changeStatus')->name('users.status');

    // Rol rotaları
    Route::resource('roles', 'App\Http\Controllers\Backend\RoleController');

    // İzin rotaları
    Route::resource('permissions', 'App\Http\Controllers\Backend\PermissionController')->except(['index', 'show']);

    // Add this new route or fix the existing one if it's incorrect:
    Route::get('api/entities', [\App\Http\Controllers\Backend\UserController::class, 'getEntities'])->name('api.entities');


});



