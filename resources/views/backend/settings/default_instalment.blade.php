@extends('backend.layouts.app')

@section('title')
    {{ $title }} - {{ __('Settings') }}
@endsection

@section('breadcrumbs')
    <x-backend-breadcrumbs>
        <x-backend-breadcrumb-item type="active" icon='fa fas fa-cog'>{{ __('Settings') }}</x-backend-breadcrumb-item>
    </x-backend-breadcrumbs>
@endsection

@section('content')
    <div class="card">
        <div class="card-body">

            <x-backend.section-header>
                <i class="fa fas fa-cog"></i> {{ $title }}
                <x-slot name="subtitle">
                    {{ $title }} düzenle
                </x-slot>
                <x-slot name="toolbar">
                    <x-backend.buttons.return-back/>
                </x-slot>
            </x-backend.section-header>


            <div class="row mt-4">
                <div class="col">

                    <form method="post" action="{{ route('backend.settings.store') }}" class="form-horizontal"
                          role="form">
                        {!! csrf_field() !!}
                        {{ html()->hidden('section')->value('update-installments') }}

                        @php
                            $banks = ['İşBank', 'YapıKredi', 'HalkBank', 'Ziraat', 'Garanti', 'Akbank', 'KuveytTurk'];
                        @endphp

                        @foreach ($banks as $bank)
                            @php
                                $inputNameCommercial = 'taksit_sayisi_'.Str::slug($bank, '_') . '_ticari';
                                $inputNameIndividual = 'taksit_sayisi_'.Str::slug($bank, '_') . '_bireysel';
                                $inputNameCommercialAdditionalInstallment = 'ek_taksit_sayisi_'.Str::slug($bank, '_') . '_ticari';
                                $inputNameIndividualAdditionalInstallment = 'ek_taksit_sayisi_'.Str::slug($bank, '_') . '_bireysel';
                                $val_individual = 0;
                                $val_commercial = 0;
                                $val_individual_add = 0;
                                $val_commercial_add = 0;
                                foreach ($pos_installments as $installment) {
                                if ($installment['name'] === $inputNameIndividual) {
                                    $val_individual = $installment['val'];
                                } elseif ($installment['name'] === $inputNameCommercialAdditionalInstallment) {
                                    $val_commercial_add = $installment['val'];
                                }elseif ($installment['name'] === $inputNameCommercial) {
                                    $val_commercial = $installment['val'];
                                }elseif ($installment['name'] === $inputNameIndividualAdditionalInstallment) {
                                    $val_individual_add = $installment['val'];
                                }
                                }
                            @endphp
                            <div class="form-group row">
                                <label class="col-md-4 col-form-label text-md-right">{{ __($bank) }} Ticari Kart</label>
                                <div class="col-md-4">
                                    <div class="form-control-wrap">
                                        <div class="form-text-hint"><span class="overline-title">Taksit</span></div>
                                        {{ html()->number($inputNameCommercial)->value($val_commercial)->class('form-control')->required() }}
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-control-wrap">
                                        <div class="form-text-hint"><span class="overline-title">Ek Taksit</span></div>
                                        {{ html()->number($inputNameCommercialAdditionalInstallment)->value($val_commercial_add)->class('form-control')->required() }}
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-md-4 col-form-label text-md-right">{{ __($bank) }} Bireysel
                                    Kart</label>
                                <div class="col-md-4">
                                    <div class="form-control-wrap">
                                        <div class="form-text-hint"><span class="overline-title">Taksit</span></div>
                                        {{ html()->number($inputNameIndividual)->value($val_individual)->class('form-control')->required() }}
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-control-wrap">
                                        <div class="form-text-hint"><span class="overline-title">Ek Taksit</span></div>
                                        {{ html()->number($inputNameIndividualAdditionalInstallment)->value($val_individual_add)->class('form-control')->required() }}
                                    </div>
                                </div>
                            </div>
                        @endforeach

                        <div class="row m-b-md">
                            <div class="col-md-12">
                                <button class="btn-primary btn">
                                    <i class='icon fas fa-save'></i> <span>@lang('Save')</span>
                                </button>
                            </div>
                        </div>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <div class="row">

            </div>
        </div>
    </div>
@endsection