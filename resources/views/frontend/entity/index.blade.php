@extends('frontend.layouts.app')

@section('title')
    {{ __('Current Account List') }}
@endsection

@section('content')


    <div class="nk-block">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title">{{ __('Current Account List') }}</h3>
                        </div><!-- .nk-block-head-content -->
                        <div class="nk-block-head-content">
                            <div class="toggle-wrap nk-block-tools-toggle">
                                <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="more-options"><em class="icon ni ni-more-v"></em></a>
                                <div class="toggle-expand-content" data-content="more-options">
                                    <ul class="nk-block-tools g-3">
                                        <li>
                                            <div class="form-control-wrap">
                                                <div class="form-icon form-icon-right">
                                                    <em class="icon ni ni-search"></em>
                                                </div>
                                                <input type="text" class="form-control" id="default-04" placeholder="{{ __('Search by company name') }}">
                                            </div>
                                        </li>
                                        <li>
                                            <div class="drodown">
                                                <a href="#" class="dropdown-toggle dropdown-indicator btn btn-outline-light btn-white" data-bs-toggle="dropdown">{{ __('Account Type') }}</a>
                                                <div class="dropdown-menu dropdown-menu-end">
                                                    <ul class="link-list-opt no-bdr">
                                                        <li><a @empty($type)class="active"@endif href="/cari"><span>{{ __('All') }}</span></a></li>
                                                        <li @if($type == '2')class="active"@endif><a href="?type=2"><span>{{ __('Customers') }}</span></a></li>
                                                        <li @if($type == '3')class="active"@endif><a href="?type=3"><span>{{ __('Suppliers') }}</span></a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </li>
                                        <li class="nk-block-tools-opt">
                                            <a href="" class="btn btn-icon btn-primary d-md-none"><em class="icon ni ni-plus"></em></a>
                                            <a href="" class="btn btn-primary d-none d-md-inline-flex"><em class="icon ni ni-plus"></em><span>{{ __('Add') }}</span></a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div><!-- .nk-block-head-content -->
                    </div><!-- .nk-block-between -->
                </div><!-- .nk-block-head -->




                <div class="nk-block">
                    <div class="row g-gs">
                        @foreach($entities as $entity)
                            <div class="col-sm-6 col-lg-4 col-xxl-3">
                                <div class="card card-bordered h-100">
                                    <div class="card-inner">
                                        <div class="project">
                                            <div class="project-head">
                                                <a href="/cari/{{ $entity->id }}" class="project-title">
                                                    <div class="user-avatar sq bg-purple"><span>{{ $entity->entity_name[2] }}</span></div>
                                                    <div class="project-info">
                                                        <h6 class="title">{{ limit_words($entity->entity_name,3) }}</h6>
                                                        <span class="sub-text">{{ $entity->entity_code }}</span>
                                                    </div>
                                                </a>

                                            </div>
                                            <div class="project-details">
                                                <p>{{ $entity->address1. ' ' . $entity->address2 }}</p>
                                            </div>
                                            @php
                                                $contract = $entity->contracts()->where('ispassive',0)->latest()->first();

                                            $used_percent = 0;
                                            if(!empty($contract->used_amount))
                                                {
                                                    if ($contract->used_amount > 0) {
            $used_percent = ($contract->used_amount / $contract->amt) * 100;
            $used_percent = number_format($used_percent, 1);
        }
                                                }

                                            @endphp
                                            <div class="project-progress">
                                                <div class="project-progress-details">
                                                    <div class="project-progress-task"><em class="icon ni ni-check-round-cut"></em><span>{{ $contract->doc_no ?? '' }} </span></div>
                                                    <div class="project-progress-percent">{{ $used_percent }}%</div>
                                                </div>
                                                <div class="progress progress-pill progress-md bg-light">
                                                    <div class="progress-bar" data-progress="{{ $used_percent }}"></div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item">{{ __('Active Contracts') }}: {{ $entity->contracts()->where('ispassive',0)->count() }}</li>
                                        <li class="list-group-item">{{ __('Open Orders') }}: {{ $entity->orders()->where('request_status','<>',4)->where('order_status',1)->count() }}</li>
                                        <li class="list-group-item">{{ __('DBS Information') }}: {{ Number::currency($entity->dbs()->whereDate('due_date','>',date('Y-m-d'))->sum('remaining_amount'), 'try','tr') }}</li>
                                        <li class="list-group-item">{{ __('Pending Payments') }}: ₺0,00</li>
                                    </ul>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div><!-- .nk-block -->

            </div>
        </div>
    </div><!-- .nk-block -->



@endsection