<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" class="js">
<head>
    <meta charset="utf-8">
    <title>Ödeme Yap</title>
    <link rel="shortcut icon" href="{{url('images/favicon.png')}}">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" type="image/ico" href="{{url('images/favicon.png')}}"/>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link rel="stylesheet" href="/assets/css/main.css">
    <link id="skin-theme" rel="stylesheet" href="/assets/css/theme.css?v={{ time() }}">
    <style>
        .credit-card {
            width: 400px;
            height: 250px;
            perspective: 1000px;
            margin: 0 auto 20px;
        }

        .credit-card-inner {
            position: relative;
            width: 100%;
            height: 100%;
            transition: transform 0.8s;
            transform-style: preserve-3d;
        }

        .credit-card.flipped .credit-card-inner {
            transform: rotateY(180deg);
        }

        .credit-card-front,
        .credit-card-back {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            border-radius: 15px;
            padding: 20px;
        }

        .credit-card-front {
            background: linear-gradient(to right, #4389A2, #5C258D);
            color: white;
        }

        .credit-card-back {
            background: linear-gradient(45deg, #5C258D, #4389A2);
            color: white;
            transform: rotateY(180deg);
        }

        .card-number {
            font-size: 1.5em;
            letter-spacing: 4px;
            margin: 40px 0 30px;
        }

        .card-holder {
            text-transform: uppercase;
            margin-bottom: 15px;
        }

        .card-expiry {
            font-size: 1.1em;
        }

        .card-strip {
            background-color: #000;
            height: 40px;
            margin: 20px 0;
        }

        .card-cvv {
            background-color: #fff;
            color: #000;
            padding: 5px;
            text-align: right;
            margin-top: 20px;
        }

        #card-form {
            max-width: 500px;
            margin: 0 auto;
        }

        .card-logo {
            width: 65px;
            height: 40px;
        }

        .chip-image {
            width: 50px;
            height: 40px;
        }
    </style>
</head>

<body class="nk-body ui-rounder npc-general ">
<div class="nk-app-root">
    <!-- main @s -->
    <div class="nk-main ">
        <!-- wrap @s -->
        <div class="nk-wrap nk-wrap-nosidebar">
            <!-- content @s -->
            <div class="nk-content ">
                <div class="nk-split nk-split-page nk-split-lg">
                    <div class="nk-split-content bg-lighter w-50 d-flex align-items-center justify-content-center justify-content-lg-end p-4 p-sm-5">
                        <div class="wide-xs w-100">
                            <div class="d-flex">
                                <a class="pe-2 d-flex align-items-center" href="/odeme">
                                    <em class="icon ni ni-arrow-left text-base"></em>
                                    <div class="logo-link ms-3">
                                        <img class="logo-light logo-img" src="/images/logo.png" srcset="/images/logo2x.png 2x" alt="logo">
                                        <img class="logo-dark logo-img" src="/images/logo-dark.png" srcset="/images/logo-dark2x.png 2x" alt="logo-dark">
                                    </div>
                                </a>
                            </div>
                            <div class="pt-4">
                                <div class="fs-4 fw-normal mb-1">{{ __('Pending Payments') }}</div>
                                <h3 class="display-4 fw-semibold">{{ Number::currency($payment->amount ?? 0, 'TRY', 'tr') }} </h3>
                                <div class="fs-5 fw-normal mt-2">{{ __('You can pay with credit card') }}</div>
                            </div>
                            <ul class="mt-4 gy-4 pb-2">
                                @isset($payment->order)
                                    <li class="d-flex justify-content-between">
                                        <div class="">
                                            <div class="caption-text">{{ $payment->order->doc_no ?? '' }} </div>
                                            <span class="sub-text">{{ __('Order amount') }}</span>
                                        </div>
                                        <div class="caption-text">{{ Number::currency($payment->amount ?? 0, 'TRY', 'tr') }}</div>
                                    </li>
                                @endif
                                <li class="d-flex justify-content-between text-success">
                                    <div class="">
                                        <div class="caption-text">{{ __('Paid Amount') }}</div>
                                    </div>
                                    <div class="caption-text">@isset($payment){{ Number::currency(($payment->amount-$payment->remaining_amount) ?? 0, 'TRY', 'tr') }}@endisset</div>
                                </li>
                            </ul>

                            <div class="d-flex justify-content-between pt-2 pb-lg-5 mb-lg-5 text-danger">
                                <div class="caption-text">{{ __('Remaining Total Amount') }}</div>
                                <div class="caption-text">@isset($payment){{ Number::currency($payment->remaining_amount ?? 0, 'TRY', 'tr') }}@endisset</div>
                            </div>

                        </div>
                    </div>
                    <div class="nk-split-content bg-white w-50 d-flex align-items-center justify-content-center justify-content-lg-start p-4 p-sm-5">
                        <div class="wide-xs w-100">



                            @if($payment_form_active)
                                @if(isset($md_error_message))
                                    <div class="alert alert-danger alert-icon"><em class="icon ni ni-alert-circle"></em> <strong>{{ __('Last Error Message') }}</strong>: {{  $md_error_message }}</div>
                                @endif
                                <h3 class="mb-3">{{ __('Pay with Card') }}</h3>
                                <div class="row g-3">

                                    <form method="POST" action="/payment/3d/form" class="is-alter form-validate">
                                        @csrf
                                        <div class="col-12">
                                            <div class="form-group">
                                                <label class="form-label" for="cardNumber">{{ __('Card information') }}</label>
                                                <div class="form-control-wrap">
                                                    <div class="d-flex flex-wrap border border-light rounded">
                                                        <div class="w-100 border-bottom border-light d-flex align-items-center">
                                                            <input class="form-control-plaintext px-3" type="text" autocomplete="off" name="number" required id="cardNumber" placeholder="1234 1234 1234 1234" />
                                                            <ul class="d-flex pe-3 gx-1 flex-shrink-0">
                                                                <li class="h-1rem d-inline-flex">
                                                                    <img src="/assets/images/icons/card/visa.png" alt="" class="h-100" />
                                                                </li>
                                                                <li class="h-1rem d-inline-flex">
                                                                    <img src="/assets/images/icons/card/mastercard.png" alt="" class="h-100" />
                                                                </li>
                                                                <li class="h-1rem d-inline-flex">
                                                                    <img src="/assets/images/icons/card/troy.png" alt="" class="h-100" />
                                                                </li>
                                                            </ul>
                                                        </div>
                                                        <div class="w-50 border-end border-light">
                                                            <div class="form-control-wrap">
                                                                <input class="form-control-plaintext px-3 date-picker-ym-x" autocomplete="off" required type="text" name="monthyear" id="cardExpiry" placeholder="{{ __('MM/YY') }}" />
                                                            </div>
                                                        </div>

                                                        <div class="w-50">
                                                            <div class="form-control-wrap">
                                                                <input class="form-control-plaintext px-3" type="text" autocomplete="off" required name="cvv" id="cardCvv" placeholder="123" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div><!-- .form-group -->
                                        </div><!-- .col -->

                                        <div class="row">
                                            <div class="col-5 mt-3">
                                                <div class="form-group">
                                                    <label class="form-label" for="name">{{ __('Name on card') }}</label>
                                                    <div class="form-control-wrap">
                                                        <input class="form-control" type="text" name="name" autocomplete="off" id="cardHolder" required placeholder="{{ __('First Last') }}" />
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-4 mt-3">
                                                <div class="form-group">
                                                    <label class="form-label" for="phone">{{ __('Mobile Phone') }}</label>
                                                    <div class="form-control-wrap">
                                                        <input class="form-control" required type="number" id="phone" value="" name="phone" />
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-3 mt-3">
                                                <div class="form-group">
                                                    <label class="form-label" for="name">{{ __('Amount to pay') }}</label>
                                                    <div class="form-control-wrap number-spinner-wrap">
                                                        <input class="form-control" required type="text" id="amount" value="@isset($payment){{ number_format($payment->remaining_amount,2,',','.') }}@endisset" name="amount" />
                                                    </div>
                                                </div>
                                            </div>

                                        </div>

                                        <span class="preview-title overline-title mt-3">{{ __('Installment Selection') }}</span>
                                        <div id="installment_content">
                                            <span class="placeholder col-12 placeholder-lg"></span>
                                            <span class="placeholder col-12 placeholder-lg"></span>
                                            <span class="placeholder col-12 placeholder-lg"></span>
                                        </div>

                                        <input type="hidden" id="type" name="type" value="visa">
                                        <input type="hidden" id="month" name="month">
                                        <input type="hidden" id="year" name="year">

                                        <input type="hidden" name="new_payment" value="{{ $new_payment ?? '' }}">
                                        <input type="hidden" name="bank" value="{{ $selectedPos ?? '' }}">

                                        <div class="row">
                                            <div class="credit-card mt-3" id="creditCard">
                                                <div class="credit-card-inner">
                                                    <div class="credit-card-front">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <svg class="chip-image" viewBox="0 0 50 40" xmlns="http://www.w3.org/2000/svg">
                                                                <rect x="5" y="5" width="40" height="30" rx="3" fill="#FFD700"/>
                                                                <rect x="10" y="15" width="30" height="3" fill="#DAA520"/>
                                                                <rect x="10" y="22" width="30" height="3" fill="#DAA520"/>
                                                            </svg>
                                                            <div class="card-logo" id="cardLogo">
                                                                <!-- Default card logo (will be replaced by JS) -->
                                                                <svg viewBox="0 0 65 40" xmlns="http://www.w3.org/2000/svg">
                                                                    <rect width="65" height="40" fill="none"/>
                                                                    <text x="5" y="25" fill="white" font-size="14">CARD</text>
                                                                </svg>
                                                            </div>
                                                        </div>
                                                        <div class="card-number" id="cardNumberDisplay">•••• •••• •••• ••••</div>
                                                        <div class="d-flex justify-content-between">
                                                            <div>
                                                                <div class="label">{{ __('Card Holder') }}</div>
                                                                <div class="card-holder" id="cardHolderDisplay">{{ __('FIRST LAST') }}</div>
                                                            </div>
                                                            <div>
                                                                <div class="label">{{ __('Expiry Date') }}</div>
                                                                <div class="card-expiry" id="cardExpiryDisplay">MM/YY</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="credit-card-back">
                                                        <div class="card-strip"></div>
                                                        <div class="card-cvv">
                                                            <div class="label">CVV</div>
                                                            <div id="cardCvvDisplay">•••</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12">
                                            <div class="p-2 border border-light rounded-2 my-1">
                                                <div class="custom-control custom-checkbox">
                                                    <input type="checkbox" class="custom-control-input" name="approveSalesContract" id="approveSalesContract" required>
                                                    <label class="custom-control-label" for="approveSalesContract">
                                                        <a href="" data-bs-target="#salesContractModal" data-bs-toggle="modal">{{ __('Distance Sales Agreement') }}</a>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="form-group">
                                                <button type="submit" class="btn btn-primary btn-block btn-submit">{{ __('Pay') }}</button>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="form-note">{{ __('Card payment uses SSL protected, secure payment infrastructure. Your card information is never saved.') }}</div>
                                        </div>
                                        <input type="hidden" name="payment_id" id="payment_id" value="{{ $payment->id ?? '' }}">
                                    </form>

                                </div><!-- .row -->
                            @else

                                <div class="alert alert-fill alert-success alert-icon"><em class="icon ni ni-check-circle"></em> <strong>{{ __('Payment Completed') }}</strong>. {{ __('The entire amount of this payment request has been paid.') }}</div>
                                
                                <div class="form-group">
                                    <a href="{{ route('odeme.index') }}" class="btn btn-lg btn-primary">{{ __('Return to Payments') }}</a>
                                </div>

                            @endif
                        </div>
                    </div>
                </div>
            </div>
            <!-- wrap @e -->
        </div>
        <!-- content @e -->
    </div>
    <!-- main @e -->
</div>
@include('frontend.order.modal.sales_contract')
<!-- app-root @e -->
<!-- JavaScript -->
<script src="/assets/js/bundle.js?v=11"></script>
<script src="/assets/js/scripts.js?v=11"></script>
<!-- select region modal -->
<script>

    $(document).ready(function() {


        $('.btn-submit').click(function(e) {
            e.preventDefault(); // Formun normal submit işlemini engelleyin

            var form = $(this).closest('form'); // Butona en yakın formu seçin

            // Kart numarasını ve CVV'yi al
            var cardNumber = form.find('input[name="number"]').val().replace(/\s+/g, ''); // Boşlukları kaldır
            var cvv = form.find('input[name="cvv"]').val();

            // Kart numarasının 16 hane olduğunu kontrol et
            if(cardNumber.length <= 15) {
                NioApp.Toast('Kart numarası en az 15 hane olmalıdır.' ,'error', {position: 'top-right'});
                return false;
            }

            // CVV'nin en az 3 rakam içerdiğinden emin ol
            if(cvv.length < 3) {

                NioApp.Toast('CVV en az 3 rakam içermelidir.', 'error', {position: 'top-right'});
                return false;
            }

            // Kart tipini bul
            var cardType = '';
            if(/^4/.test(cardNumber)) {
                cardType = 'Visa';
            } else if(/^5[1-5]/.test(cardNumber)) {
                cardType = 'MasterCard';
            } else {
                cardType = null;
            }

            // Kart tipini type inputuna ata
            form.find('input[name="type"]').val(cardType);

            // monthyear inputundan ay ve yıl bilgisini al ve ayır
            var monthYear = form.find('input[name="monthyear"]').val().split('/');
            if(monthYear.length === 2) {
                var month = monthYear[0];
                var year = '20' + monthYear[1]; // YY formatını YYYY formatına çevir

                // Gizli input alanlarına ay ve yıl değerlerini ata
                form.find('input[name="month"]').val(month);
                form.find('input[name="year"]').val(year);
            } else {

                NioApp.Toast('Ay/Yıl formatı hatalı.', 'error', {position: 'top-right'});
                return false;
            }

            // Formu gönder
            form.submit();
        });

        $('#cardNumber').on('input', function() {
            // Remove any spaces from previous inputs to get a clean number
            var inputVal = $(this).val().replace(/\s+/g, '');

            // Insert space after every 4 characters
            var formattedInputVal = inputVal.replace(/(\d{4})(?=\d)/g, '$1 ');

            // Update the input field with formatted value
            $(this).val(formattedInputVal);
        });
    });

    const creditCard = document.getElementById('creditCard');
    const cardNumber = document.getElementById('cardNumber');
    const cardNumberDisplay = document.getElementById('cardNumberDisplay');
    const cardHolder = document.getElementById('cardHolder');
    const cardHolderDisplay = document.getElementById('cardHolderDisplay');
    const cardExpiry = document.getElementById('cardExpiry');
    const cardExpiryDisplay = document.getElementById('cardExpiryDisplay');
    const cardCvv = document.getElementById('cardCvv');
    const cardCvvDisplay = document.getElementById('cardCvvDisplay');
    const cardLogo = document.getElementById('cardLogo');

    const cardLogos = {
        visa: `
                    <svg viewBox="0 0 65 40" xmlns="http://www.w3.org/2000/svg">
                        <path d="M0 15 L65 15 L60 25 L5 25 Z" fill="#fff"/>
                        <text x="5" y="35" fill="white" font-size="14" font-weight="bold">VISA</text>
                    </svg>
                `,
        mastercard: `
                    <svg viewBox="0 0 65 40" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="20" cy="20" r="12" fill="#EB001B"/>
                        <circle cx="35" cy="20" r="12" fill="#F79E1B" fill-opacity="0.8"/>
                        <text x="0" y="35" fill="white" font-size="10">MASTERCARD</text>
                    </svg>
                `,
        amex: `
                    <svg viewBox="0 0 65 40" xmlns="http://www.w3.org/2000/svg">
                        <rect width="65" height="30" fill="#27AEE3"/>
                        <text x="5" y="35" fill="white" font-size="12">AMEX</text>
                    </svg>
                `,
        discover: `
                    <svg viewBox="0 0 65 40" xmlns="http://www.w3.org/2000/svg">
                        <path d="M0 0 L65 0 L65 40 L0 40 Z" fill="#FF6600"/>
                        <text x="5" y="25" fill="white" font-size="12">DISCOVER</text>
                    </svg>
                `,
        default: `
                    <svg viewBox="0 0 65 40" xmlns="http://www.w3.org/2000/svg">
                        <rect width="65" height="40" fill="none"/>
                        <text x="5" y="25" fill="white" font-size="14">CARD</text>
                    </svg>
                `
    };

    // Kart tipini tespit etme fonksiyonu
    function detectCardType(number) {
        const cleanNumber = number.replace(/\D/g, '');

        if (cleanNumber.startsWith('4')) {
            return 'visa';
        } else if (/^5[1-5]/.test(cleanNumber)) {
            return 'mastercard';
        } else if (/^3[47]/.test(cleanNumber)) {
            return 'amex';
        } else if (/^6(?:011|5)/.test(cleanNumber)) {
            return 'discover';
        }

        return 'default';
    }

    $('#cardNumber').on('input', function() {
        var cardNumber = $(this).val().replace(/\s+/g, '');
        var paymentId = $("#payment_id").val();
        var amount = $("#amount").val().replace(/\s+/g, '').replace(',', '.'); // Virgülü noktaya çevir
        if (cardNumber.length == 6 ||cardNumber.length == 16) {
            $.ajax({
                url: '/get-installment',
                method: 'GET',
                data: { card_number: cardNumber, payment_id: paymentId, amount: amount },
                success: function (response) {
                    $("#overlay").fadeOut(200);
                    if (response.success === true) {
                        $('#installment_content').html(response.html);
                    } else {
                        $("#overlay").fadeOut(100);
                        toastr.clear();
                        NioApp.Toast(response.message, 'error', {position: 'top-center'});
                    }
                },
                error: function(xhr) {
                    console.error(xhr);
                }
            });
        }
    });

    // Amount input için Türkçe formatlama (sadece binlik ayraç)
    $('#amount').on('input', function() {
        let input = $(this);
        let value = input.val();
        
        // Sadece sayıları ve virgül kabul et
        value = value.replace(/[^\d,]/g, '');
        
        // Birden fazla virgül varsa sadece ilkini tut
        let commaIndex = value.indexOf(',');
        if (commaIndex !== -1) {
            value = value.substring(0, commaIndex + 1) + value.substring(commaIndex + 1).replace(/,/g, '');
        }
        
        // Binlik ayraç ekleme (ondalık kısım hariç)
        let [integerPart, decimalPart] = value.split(',');
        
        // Binlik ayraç olarak nokta ekle
        integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
        
        // Sonucu birleştir
        let formattedValue = integerPart;
        if (decimalPart !== undefined) {
            // Ondalık kısım en fazla 2 hane
            decimalPart = decimalPart.substr(0, 2);
            formattedValue += ',' + decimalPart;
        }
        
        input.val(formattedValue);
        
        // API çağrısı için formatlanmamış değeri hazırla
        var cardNumber = $("#cardNumber").val().replace(/\s+/g, '');
        var amount = formattedValue.replace(/\./g, '').replace(',', '.'); // Noktaları kaldır, virgülü noktaya çevir
        var paymentId = $("#payment_id").val();
        if(amount>0){
            $.ajax({
                url: '/get-installment',
                method: 'GET',
                data: { card_number: cardNumber,amount: amount, payment_id: paymentId },
                success: function (response) {
                    $("#overlay").fadeOut(200);
                    if (response.success === true) {
                        //$('#amount').val(response.amount);
                        $('#installment_content').html(response.html);
                    } else {
                        $("#overlay").fadeOut(100);
                        toastr.clear();
                        NioApp.Toast(response.message, 'error', {position: 'top-center'});
                        $('#amount').val(response.remaining_amount);
                    }
                },
                error: function(xhr) {
                    console.error(xhr);
                }
            });
        }
    });

    cardNumber.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\s/g, '').replace(/\D/g, '');
        let formattedValue = '';

        for(let i = 0; i < value.length; i++) {
            if(i > 0 && i % 4 === 0) {
                formattedValue += ' ';
            }
            formattedValue += value[i];
        }

        value = formattedValue;
        cardNumberDisplay.textContent = formattedValue || '•••• •••• •••• ••••';

        const cardType = detectCardType(value);
        $('input[name="type"]').val(cardType);

        cardLogo.innerHTML = cardLogos[cardType];

    });

    cardHolder.addEventListener('input', function(e) {
        let value = e.target.value.toUpperCase();
        cardHolderDisplay.textContent = value || 'AD SOYAD';
    });

    cardExpiry.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length > 2) {
            value = value.slice(0,2) + '/' + value.slice(2);
        }
        e.target.value = value;
        cardExpiryDisplay.textContent = value || 'MM/YY';
    });

    $('#cardExpiry').on('change', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length > 2) {
            value = value.slice(0, 2) + '/' + value.slice(2);
        }
        e.target.value = value;
        cardExpiryDisplay.textContent = value || 'MM/YY';
    });


    cardCvv.addEventListener('focus', function() {
        creditCard.classList.add('flipped');
    });

    cardCvv.addEventListener('blur', function() {
        creditCard.classList.remove('flipped');
    });

    cardCvv.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        e.target.value = value;
        cardCvvDisplay.textContent = value || '•••';
    });
</script>
</body>
</html>