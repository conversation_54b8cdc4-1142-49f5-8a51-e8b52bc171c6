<div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
        <div class="modal-header">
            <h4 class="modal-title">{{ __('Offer Details') }} - {{ $offer->doc_no }}</h4>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">{{ __('General Information') }}</h6>
                            <p><strong>{{ __('Document No') }}:</strong> {{ $offer->doc_no }}</p>
                            <p><strong>{{ __('Date') }}:</strong> {{ date('d.m.Y', strtotime($offer->doc_date)) }}</p>
                            <p><strong>{{ __('Total Amount') }}:</strong> {{ Number::currency($offer->amt, $offer->cur_code ?? 'TRY', 'tr') }}</p>
                            <p><strong>{{ __('VAT Amount') }}:</strong> {{ Number::currency($offer->amt_vat, $offer->cur_code ?? 'TRY', 'tr') }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">{{ __('Customer Information') }}</h6>
                            <p><strong>{{ __('Customer') }}:</strong> {{ $offer->entity->entity_name ?? '' }}</p>
                            <p><strong>{{ __('Company') }}:</strong> {{ $offer->company->co_desc ?? '' }}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">{{ __('Offer Items') }}</h6>
                    @if($offerDetails)

                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead>
                                    <tr>
                                        <th>{{ __('Line') }}</th>
                                        <th>{{ __('Item Code') }}</th>
                                        <th>{{ __('Item Name') }}</th>
                                        <th>{{ __('Quantity') }}</th>
                                        <th>{{ __('Unit Price') }}</th>
                                        <th>{{ __('Amount') }}</th>
                                        <th>{{ __('VAT Amount') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($offerDetails as $detail)

                                        <tr>
                                            <td>{{ $detail['line_no'] }}</td>
                                            <td>{{ $detail['item_code'] }}</td>
                                            <td>{{ $detail['item_name'] }}</td>
                                            <td>{{ number_format($detail['qty'], 2) }}</td>
                                            <td>{{ Illuminate\Support\Number::currency($detail['unit_price'], $offer->cur_code ?? 'TRY', 'tr') }}</td>
                                            <td>{{ Illuminate\Support\Number::currency($detail['amt'], $offer->cur_code ?? 'TRY', 'tr') }}</td>
                                            <td>{{ Illuminate\Support\Number::currency($detail['amt_vat'], $offer->cur_code ?? 'TRY', 'tr') }}</td>
                                        </tr>

                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-warning">
                            {{ __('No offer details found.') }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
        </div>
    </div>
</div>