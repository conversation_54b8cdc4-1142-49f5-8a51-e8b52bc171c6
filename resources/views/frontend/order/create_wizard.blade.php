@extends('frontend.layouts.app')

@section('title')
    {{ __('Create Order') }}
@endsection
@section('css')
    <style>
        .credit-card {
            width: 400px;
            height: 250px;
            perspective: 1000px;
            margin: 0 auto 20px;
        }

        .credit-card-inner {
            position: relative;
            width: 100%;
            height: 100%;
            transition: transform 0.8s;
            transform-style: preserve-3d;
        }

        .credit-card.flipped .credit-card-inner {
            transform: rotateY(180deg);
        }

        .credit-card-front,
        .credit-card-back {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            border-radius: 15px;
            padding: 20px;
        }

        .credit-card-front {
            background: linear-gradient(to right, #4389A2, #5C258D);
            color: white;
        }

        .credit-card-back {
            background: linear-gradient(45deg, #5C258D, #4389A2);
            color: white;
            transform: rotateY(180deg);
        }

        .card-number {
            font-size: 1.5em;
            letter-spacing: 4px;
            margin: 40px 0 30px;
        }

        .card-holder {
            text-transform: uppercase;
            margin-bottom: 15px;
        }

        .card-expiry {
            font-size: 1.1em;
        }

        .card-strip {
            background-color: #000;
            height: 40px;
            margin: 20px 0;
        }

        .card-cvv {
            background-color: #fff;
            color: #000;
            padding: 5px;
            text-align: right;
            margin-top: 20px;
        }

        #card-form {
            max-width: 500px;
            margin: 0 auto;
        }

        .card-logo {
            width: 65px;
            height: 40px;
        }

        .chip-image {
            width: 50px;
            height: 40px;
        }
    </style>
@endsection
@section('content')
    <!-- content @s -->
    <div class="nk-content ">
        <div class="container-fluid">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="components-preview wide-lg mx-auto">
                        <div class="nk-block-head nk-block-head-sm">
                            <div class="nk-block-between">
                                <div class="nk-block-head-content"><h3 class="nk-block-title page-title">{{ __('Create Order') }}</h3></div>
                                <div class="nk-block-head-content">
                                    <div class="toggle-wrap nk-block-tools-toggle"><a href="#"
                                                                                      class="btn btn-icon btn-trigger toggle-expand me-n1"
                                                                                      data-target="pageMenu"><em
                                                    class="icon ni ni-more-v"></em></a>
                                        <div class="toggle-expand-content" data-content="pageMenu">
                                            <ul class="nk-block-tools g-3">
                                                <li class="nk-block-tools-opt"><a href="/siparis" class="btn btn-white btn-dim btn-outline-light" target="_blank"><em class="icon ni ni-reports"></em><span>{{ __('Orders') }}</span></a></li>
                                                <li>
                                                    <a href="{{ route('order.shipment_report')}}" target="_blank" class="btn btn-icon btn-dim btn-secondary d-md-none">
                                                        <em class="icon ni ni-truck"></em></a>
                                                    <a href="{{ route('order.shipment_report')}}" target="_blank" class="btn btn-dim btn-secondary d-none d-md-inline-flex"><em class="icon ni ni-truck"></em><span>{{ __('Order Shipment Report') }}</span>
                                                    </a>
                                                </li>

                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="nk-block-head nk-block-head-lg wide-sm">
                            <div class="nk-block-head-content">

                                <div class="nk-block-des">

                                </div>
                            </div>
                        </div><!-- .nk-block-head -->
                        <div class="nk-block nk-block-lg">
                            <div class="card card-bordered">
                                {{--                                <form action="{{ action('App\Http\Controllers\Frontend\CartController@store') }}" method="post" class="nk-stepper stepper-init is-alter nk-stepper-s1" id="order_form">--}}
                                <form action="/payment/3d/form" method="post"
                                      class="nk-stepper stepper-init is-alter nk-stepper-s1" id="order_form" @isset($user_cart)data-step-init="2"@endisset>
                                    @csrf

                                    <div class="row g-0 col-sep col-sep-md col-sep-xl">
                                        <div class="col-md-3 col-xl-3">
                                            <div class="card-inner">
                                                <ul class="nk-stepper-nav nk-stepper-nav-s1 stepper-nav is-vr">
                                                    <li>
                                                        <div class="step-item">
                                                            <div class="step-text">
                                                                <div class="lead-text">{{ __('Contract') }}</div>
                                                                <div class="sub-text" id="step1_sub_title">
                                                                @isset($user_cart)
                                                                    @if($user_cart->contract_id == 0)
                                                                        {{ $latest_price_list }}
                                                                    @else
                                                                        @php
                                                                            $selected_contract = $contracts->where('id', $user_cart->contract_id)->first();
                                                                        @endphp
                                                                        @if($selected_contract)
                                                                            {{ $selected_contract->doc_no }}
                                                                        @else
                                                                            {{ __('Contract/Ton Price Selection') }}
                                                                        @endif
                                                                    @endif
                                                                @else
                                                                    {{ __('Contract/Ton Price Selection') }}
                                                                @endisset
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>

                                                    <li>
                                                        <div class="step-item">
                                                            <div class="step-text">
                                                                <div class="lead-text">{{ __('Order Entry') }}</div>
                                                                <div class="sub-text">{{ __('Product Selection') }}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <div class="step-item">
                                                            <div class="step-text">
                                                                <div class="lead-text">{{ __('Order Confirmation') }}</div>
                                                                <div class="sub-text" id="step3_sub_title">
                                                                @isset($user_cart)
                                                                    {{ $user_cart->cart_items->count() }} {{ __('items in cart') }}
                                                                @else
                                                                    {{ __('Cart Operations') }}
                                                                @endisset
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <div class="step-item">
                                                            <div class="step-text">
                                                                <div class="lead-text">{{ __('Shipment Information') }}</div>
                                                                <div class="sub-text">{{ __('Shipment Address and Date') }}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <div class="step-item">
                                                            <div class="step-text">
                                                                <div class="lead-text">{{ __('Payment') }}</div>
                                                                <div class="sub-text" id="step5_sub_title">
                                                                @isset($user_cart)
                                                                    {{ Number::currency(($user_cart->cart_items->sum('amt') + $user_cart->cart_items->sum('amt_vat')), 'TRY', 'tr') }}
                                                                @else
                                                                    {{ __('Payment Operations') }}
                                                                @endisset
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <div class="step-item">
                                                            <div class="step-text">
                                                                <div class="lead-text">{{ __('Completed') }}</div>
                                                                <div class="sub-text">{{ __('Order Created') }}</div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="col-md-9 col-xl-9">
                                            <div class="card-inner">
                                                <div class="nk-stepper-content">
                                                    <div class="nk-stepper-steps stepper-steps">

                                                        <div class="nk-stepper-step">
                                                            <h5 class="title mb-3">{{ __('Contract Information') }}</h5>

                                                            <div class="row g-3">
                                                                <div class="col-12">

                                                                    <div class="form-group">
                                                                        <label class="form-label"
                                                                               for="contract_id">{{ $user->active_entity->entity_name ?? '' }}
                                                                            {{ __('Active Contracts') }}</label>
                                                                        <div class="form-control-wrap">
                                                                            <select class="form-select js-select2" id="contract_id" data-search="on" name="contract_id" required>
                                                                                <option value="">{{ __('Price List / Contract Selection') }}</option>
                                                                                @if($general_price_list === 'on')
                                                                                    <option value="0"
                                                                                    @isset($user_cart)
                                                                                        @if($user_cart->contract_id == 0){{'selected'}}@endif
                                                                                            @endisset
                                                                                    >{{ $latest_price_list }}</option>
                                                                                @endif
                                                                                @if($order_with_contract === 'on')
                                                                                    @foreach($contracts as $contract)
                                                                                        <option value="{{ $contract->id }}"
                                                                                        @isset($user_cart)
                                                                                            @if($user_cart->contract_id == $contract->id){{'selected'}}@endif
                                                                                                @endisset
                                                                                        >
                                                                                            <b>{{ $contract->doc_no }}</b>
                                                                                            - {{ $contract->doc_description }}
                                                                                            - {{ $contract->zz_ton_price ? Illuminate\Support\Number::currency($contract->zz_ton_price,$contract->currency->cur_code,'tr') : '0.00' }}
                                                                                            - {{ __('Remaining Amount') }}: {{ $contract->remaining_amount ? Illuminate\Support\Number::currency($contract->remaining_amount,'try','tr') : '0.00' }}
                                                                                        </option>
                                                                                    @endforeach

                                                                                @endif
                                                                            </select>
                                                                        </div>
                                                                    </div>
                                                                </div>


                                                                <div id="contract_content">
                                                                    <div class="card card-bordered">
                                                                        <div class="card-inner">
                                                                            <div class="project">
                                                                                <div class="project-head">
                                                                                    <a href="#" class="project-title">
                                                                                        <div class="user-avatar sq bg-purple">
                                                                                            <span class=""> </span>
                                                                                        </div>
                                                                                        <div class="project-info">
                                                                                            <h6 class="title">
                                                                                                {{ __('Select Contract') }}</h6>
                                                                                            <span class="sub-text placeholder">{{ __('Start') }}: 31.05.2024 - {{ __('End') }}: 31.08.2024</span>
                                                                                        </div>
                                                                                    </a>
                                                                                    <div class="drodown placeholder">
                                                                                        <a href="#"
                                                                                           class="dropdown-toggle btn btn-sm btn-icon btn-trigger mt-n1 me-n1"
                                                                                           data-bs-toggle="dropdown"><em
                                                                                                    class="icon ni ni-more-h"></em></a>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="project-details placeholder">
                                                                                    <p>{{ __('DBS Contract Term 66 Days / Ton Price 20,500 TL') }}</p>
                                                                                </div>
                                                                                <div class="project-progress">
                                                                                    <div class="project-progress-details">
                                                                                        <div class="project-progress-task placeholder">
                                                                                            <em class="icon ni ni-check-round-cut"></em>
                                                                                            <span>{{ __('Contract Amount') }}: 2,000,000 TRY / {{ __('Used') }}: 0 TRY </span>
                                                                                        </div>
                                                                                        <div class="project-progress-percent">
                                                                                            0%
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="progress progress-pill progress-md bg-light">
                                                                                        <div class="progress-bar"
                                                                                             data-progress="0"
                                                                                             style="width: 0%;"></div>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="project-meta">
                                                                                    <ul class="project-users g-1">
                                                                                        <li>
                                                                                            <div class="badge badge-dim sm bg-primary">
                                                                                                <span class="placeholder">B2B-2000008617</span>
                                                                                            </div>
                                                                                        </li>
                                                                                    </ul>
                                                                                    <span class="badge badge-dim bg-success"><em
                                                                                                class="icon ni ni-clock"></em><span
                                                                                                class="placeholder">{{ __('2 months later') }}</span></span>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                            </div>
                                                        </div>


                                                        <div class="nk-stepper-step">
                                                            <h5 class="title mb-3">{{ __('Order Entry') }}</h5>
                                                            <div class="row g-3">
                                                                <div class="col-sm-3">
                                                                    <div class="form-group">
                                                                        <label class="form-label" for="category_id">{{ __('Product Group') }}
                                                                            *</label>
                                                                        <div class="form-control-wrap">
                                                                            <select name="category_id"
                                                                                    class="form-select js-select2"
                                                                                    data-placeholder="{{ __('Select') }}"
                                                                                    id="category_id">
                                                                                <option value="">{{ __('Select') }}</option>
                                                                                @foreach($categories as $category)
                                                                                    <option value="{{ $category->id }}">{{ $category->description }}</option>
                                                                                @endforeach

                                                                            </select>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="col-sm-3">
                                                                    <div class="form-group">
                                                                        <label class="form-label"
                                                                               for="density">{{ __('Density') }}</label>
                                                                        <div class="form-control-wrap">
                                                                            <select name="density" class="form-select js-select2" data-placeholder="{{ __('Select') }}" id="density"></select>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="col-sm-2">
                                                                    <div class="form-group">
                                                                        <label class="form-label"
                                                                               for="depth">{{ __('Thickness') }}</label>
                                                                        <div class="form-control-wrap">
                                                                            <select name="depth"
                                                                                    class="form-select js-select2"
                                                                                    data-placeholder="{{ __('Select') }}"
                                                                                    id="depth"></select>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <div class="col-sm-2">
                                                                    <div class="form-group">
                                                                        <label class="form-label"
                                                                               for="height">{{ __('Width') }}</label>
                                                                        <div class="form-control-wrap">
                                                                            <select name="height" class="form-select js-select2" data-placeholder="{{ __('Select') }}" id="height"></select>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="col-sm-2">
                                                                    <div class="form-group">
                                                                        <label class="form-label" for="width">{{ __('Length') }}</label>
                                                                        <div class="form-control-wrap">
                                                                            <select name="width" class="form-select js-select2" data-placeholder="{{ __('Select') }}" id="width"></select>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="col-sm-12">
                                                                    <div class="form-group">
                                                                        <label class="form-label" for="product_id">{{ __('Product') }}
                                                                            *</label>
                                                                        <div class="form-control-wrap">
                                                                            <select class="form-select js-select2" data-search="on" id="product_id" name="product_id" data-placeholder="{{ __('Select Product Type') }}"></select>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="card card-bordered card-preview">

                                                                    <div id="product_content">
                                                                        <table class="table table-stripped">
                                                                            <thead>
                                                                            <tr>

                                                                                <th>{{ __('DENSITY') }}</th>
                                                                                <th>{{ __('UNIT') }}</th>
                                                                                <th>{{ __('THICKNESS') }}</th>
                                                                                <th>{{ __('WIDTH X LENGTH') }}</th>
                                                                                <th>{{ __('COATING') }}</th>
                                                                            </tr>
                                                                            </thead>
                                                                            <tbody>
                                                                            <tr>

                                                                                <td>0</td>
                                                                                <td>KG/M2</td>
                                                                                <td>0 MM</td>
                                                                                <td>0 MM</td>
                                                                                <td>{{ __('OTHER') }}</td>
                                                                            </tr>

                                                                            </tbody>
                                                                        </table>
                                                                    </div>
                                                                    <div class="card-inner">
                                                                        <div class="row" id="product_quantity_content">
                                                                            <div class="col-md-4">
                                                                                <div class="row g-4">
                                                                                    <div class="col-12">
                                                                                        <div class="form-control-wrap">
                                                                                            <div class="input-group">
                                                                                                <input
                                                                                                        type="number"
                                                                                                        name="qty"
                                                                                                        id="qty"
                                                                                                        class="form-control"
                                                                                                        disabled
                                                                                                        placeholder="{{ __('Unit') }}">
                                                                                                <div class="input-group-append">
                                                                                                    <button class="btn btn-dim btn-dark"
                                                                                                            disabled
                                                                                                            style="min-width: 80px;">
                                                                                                        M<sup>2</sup>
                                                                                                    </button>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="col-12">
                                                                                        <div class="form-control-wrap">
                                                                                            <div class="input-group">
                                                                                                <input
                                                                                                        type="number"
                                                                                                        name="quantity_pallet"
                                                                                                        id="quantity_pallet"
                                                                                                        class="form-control"
                                                                                                        disabled
                                                                                                        placeholder="{{ __('Unit') }}">
                                                                                                <div class="input-group-append">
                                                                                                    <button class="btn btn-dim btn-dark"
                                                                                                            disabled
                                                                                                            style="min-width: 80px;">
                                                                                                        {{ __('PALLET') }}
                                                                                                    </button>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="col-12">
                                                                                        <div class="form-control-wrap">
                                                                                            <div class="input-group">
                                                                                                <input
                                                                                                        type="number"
                                                                                                        name="quantity_truck"
                                                                                                        id="quantity_truck"
                                                                                                        class="form-control"
                                                                                                        disabled
                                                                                                        placeholder="{{ __('Unit') }}">
                                                                                                <div class="input-group-append">
                                                                                                    <button class="btn btn-dim btn-dark"
                                                                                                            disabled
                                                                                                            style="min-width: 80px;">
                                                                                                        {{ __('TRUCK') }}
                                                                                                    </button>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-md-8">
                                                                                <div class="row g-4">
                                                                                    <div class="col-12">
                                                                                        <div class="form-control-wrap">
                                                                                            <div class="input-group">
                                                                                                <input
                                                                                                        type="text"
                                                                                                        id="amt"
                                                                                                        name="amt"
                                                                                                        class="form-control"
                                                                                                        placeholder="{{ __('Total Amount') }}">
                                                                                                <div class="input-group-append">
                                                                                                    <button class="btn btn-primary"
                                                                                                            disabled
                                                                                                            style="min-width: 140px;">
                                                                                                        {{ __('TOTAL') }}
                                                                                                    </button>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="col-12">
                                                                                        <div class="form-control-wrap">
                                                                                            <div class="input-group">
                                                                                                <input
                                                                                                        type="text"
                                                                                                        class="form-control"
                                                                                                        id="amt_vat"
                                                                                                        name="amt_vat"
                                                                                                        placeholder="{{ __('VAT') }}">
                                                                                                <div class="input-group-append">
                                                                                                    <button class="btn btn-primary"
                                                                                                            disabled
                                                                                                            style="min-width: 140px;">
                                                                                                        {{ __('VAT') }}
                                                                                                    </button>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="col-12">
                                                                                        <div class="form-control-wrap">
                                                                                            <div class="input-group">
                                                                                                <input
                                                                                                        type="text"
                                                                                                        class="form-control"
                                                                                                        id="grand_total"
                                                                                                        placeholder="{{ __('Grand Total') }}">
                                                                                                <div class="input-group-append">
                                                                                                    <button class="btn btn-primary"
                                                                                                            disabled
                                                                                                            style="min-width: 140px;">
                                                                                                        {{ __('GRAND TOTAL') }}
                                                                                                    </button>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>

                                                                            </div>
                                                                        </div><!-- .card-preview -->
                                                                    </div>
                                                                    <input type="hidden" id="unit_price_tra" name="unit_price_tra">
                                                                    <div class="card-footer">
                                                                        <button type="button"
                                                                                class="btn btn-block btn-primary"
                                                                                id="btn-add-to-cart" disabled><span>{{ __('Add to Cart') }}</span><em
                                                                                    class="icon ni ni-cart"></em>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="nk-stepper-step">
                                                            <h5 class="title mb-3">{{ __('Order Confirmation') }}</h5>


                                                            <div class="mb-3" id="cart_content"></div>

                                                        </div>

                                                        <div class="nk-stepper-step">
                                                            <h5 class="title mb-3">{{ __('Shipment Information') }}</h5>
                                                            <div class="row mb-2">
                                                                <div class="col-sm-4">
                                                                    <div class="form-group">
                                                                        <label class="form-label" for="country_id">{{ __('Country') }}
                                                                            *</label>
                                                                        <div class="form-control-wrap">
                                                                            <select name="country_id"

                                                                                    class="form-select js-select2"
                                                                                    id="country_id">
                                                                                <option value="">{{ __('Country') }}</option>
                                                                                @foreach($countries as $country)
                                                                                    <option value="{{ $country->id }}" @if($country->id == 103){{'selected'}}@endif>{{ $country->country_name }}</option>
                                                                                @endforeach
                                                                            </select>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <div class="col-sm-4">
                                                                    <div class="form-group">
                                                                        <label class="form-label" for="city_id">{{ __('City') }}
                                                                            *</label>
                                                                        <div class="form-control-wrap">
                                                                            <select name="city_id"
                                                                                    required
                                                                                    class="form-select js-select2"
                                                                                    id="city_id">
                                                                                <option value="">{{ __('City') }}</option>
                                                                                @foreach($cities as $city)
                                                                                    <option value="{{ $city->id }}">{{ $city->city_name }}</option>
                                                                                @endforeach
                                                                            </select>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <div class="col-sm-4">
                                                                    <div class="form-group">
                                                                        <label class="form-label" for="town_id">{{ __('Town') }}
                                                                            *</label>
                                                                        <div class="form-control-wrap">
                                                                            <select name="town_id" id="town_id" class="form-select js-select2" data-search="on" style="width:100%">
                                                                                <option value="">{{ __('All') }}</option>
                                                                                <!-- İlçe seçenekleri JavaScript ile doldurulacak -->
                                                                            </select>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            {{--                                                            <div class="form-control-wrap mb-2">--}}
                                                            {{--                                                                <div class="input-group">--}}
                                                            {{--                                                                    <div class="input-group-prepend w-20">--}}
                                                            {{--                                                                        <span class="input-group-text w-100">{{ __('SHIPMENT ADDRESS') }}</span>--}}
                                                            {{--                                                                    </div>--}}
                                                            {{--                                                                    <select name="shipping_address_id"--}}
                                                            {{--                                                                            required--}}
                                                            {{--                                                                            class="form-control"--}}
                                                            {{--                                                                            id="shipping_address_id">--}}
                                                            {{--                                                                        <option value="">{{ __('Select Shipment Address') }}</option>--}}
                                                            {{--                                                                        @foreach($user->active_entity->addresses as $address)--}}
                                                            {{--                                                                            <option value="{{ $address->id }}">{{ $address->title }}--}}
                                                            {{--                                                                                : {{ $address->address1 . ' ' . $address->address2 }}</option>--}}
                                                            {{--                                                                        @endforeach--}}
                                                            {{--                                                                    </select>--}}

                                                            {{--                                                                    <div class="input-group-append z-1">--}}
                                                            {{--                                                                        <a data-href="{{ action('App\Http\Controllers\Frontend\AddressController@create') }}"--}}
                                                            {{--                                                                           data-container="#modal_container"--}}
                                                            {{--                                                                           class="btn btn-outline-primary btn-dim btn-modal">--}}
                                                            {{--                                                                            <i class="ni ni-plus-circle-fill"></i>--}}
                                                            {{--                                                                        </a>--}}
                                                            {{--                                                                    </div>--}}
                                                            {{--                                                                </div>--}}
                                                            {{--                                                            </div>--}}

                                                            <div class="form-control-wrap mb-2">
                                                                <div class="input-group">
                                                                    <div class="input-group-prepend w-20"><span
                                                                                class="input-group-text w-100">{{ __('SHIPMENT DATE') }}</span>
                                                                    </div>
                                                                    <input type="text" class="form-control date-pickers"
                                                                           id="shipping_date" name="shipping_date" required
                                                                           data-date-start-date="{{ date('d.m.Y') }}"
                                                                           data-date-end-date=""
                                                                           placeholder="gg.aa.yyyy"
                                                                           data-date-format="dd.mm.yyyy">
                                                                </div>
                                                            </div>


                                                            <input type="hidden" name="incoterms_id" value="37">
                                                            {{--                                                            @if($warehouse_delivery_only === 'off')--}}
                                                            {{--                                                                <div class="form-control-wrap mb-2">--}}
                                                            {{--                                                                    <div class="input-group">--}}
                                                            {{--                                                                        <div class="input-group-prepend w-20"><span--}}
                                                            {{--                                                                                    class="input-group-text w-100">{{ __('SHIPMENT TYPE') }}</span>--}}
                                                            {{--                                                                        </div>--}}
                                                            {{--                                                                        <select name="incoterms_id" class="form-select" required--}}
                                                            {{--                                                                                id="transport_type_select">--}}
                                                            {{--                                                                            <option value="">{{ __('All') }}</option>--}}
                                                            {{--                                                                            <option value="37">{{ __('By Buyer') }}</option>--}}
                                                            {{--                                                                            <option value="52">{{ __('By Seller') }}</option>--}}
                                                            {{--                                                                        </select>--}}
                                                            {{--                                                                    </div>--}}
                                                            {{--                                                                </div>--}}
                                                            {{--                                                            @endif--}}

                                                            <div class="form-control-wrap mb-2 d-none"
                                                                 id="shipping_price_div">
                                                                <div class="input-group">
                                                                    <div class="input-group-prepend w-20"><span
                                                                                class="input-group-text w-100">{{ __('Shipping Cost') }}</span>
                                                                    </div>

                                                                    <input type="text" name="shipping_price" id="shipping_price_input"
                                                                           class="form-control"
                                                                           placeholder="{{ __('Select Address') }}" disabled>

                                                                </div>
                                                            </div>

                                                            <div class="form-control-wrap mb-2">
                                                                <div class="input-group">
                                                                    <div class="input-group-prepend w-20"><span
                                                                                class="input-group-text w-100">{{ __('DESCRIPTION') }}</span>
                                                                    </div>
                                                                    <input class="form-control" name="notes" id="notes"
                                                                           placeholder="{{ __('Description Field') }}" value="">
                                                                </div>
                                                            </div>

                                                            <div class="alert alert-icon alert-info mt-3" role="alert">
                                                                <em class="icon ni ni-alert-circle"></em> <strong>{{ __('Please Note') }}</strong>! {{ __('The information must be entered correctly for the shipment invoice. Any penalties arising from incorrect or incomplete addresses will be charged to you.') }}
                                                                <div class="form-group mt-3">
                                                                    <div class="custom-control custom-checkbox">
                                                                        <input type="checkbox" class="custom-control-input" name="terms_accepted" id="terms_accepted" required>
                                                                        <label class="custom-control-label" for="terms_accepted">
                                                                            {{ __('Okudum anladım, kabul ediyorum') }}
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="nk-stepper-step">
                                                            <div class="card card-bordered card-preview">
                                                                <div class="card-inner" id="payment-types">
                                                                    <ul class="nav nav-tabs mt-n3" role="tablist">
                                                                        <li class="nav-item d-none" role="presentation" id="tab-cc"><a
                                                                                    class="nav-link"
                                                                                    data-bs-toggle="tab"
                                                                                    href="#tabItem5"
                                                                                    aria-selected="true" role="tab"><em
                                                                                        class="icon ni ni-cc"></em><span>{{ __('Credit Card') }}</span></a>
                                                                        </li>
                                                                        <li class="nav-item d-none" role="presentation" id="tab-eft"><a
                                                                                    class="nav-link"
                                                                                    data-bs-toggle="tab"
                                                                                    href="#tabItem6"
                                                                                    aria-selected="false"
                                                                                    tabindex="-1" role="tab"><em
                                                                                        class="icon ni ni-tranx"></em><span>{{ __('EFT / Wire Transfer') }}</span></a>
                                                                        </li>
                                                                        <li class="nav-item d-none" role="presentation" id="tab-dbs"><a
                                                                                    class="nav-link"
                                                                                    data-bs-toggle="tab"
                                                                                    href="#tabItem7"
                                                                                    aria-selected="false"
                                                                                    id="dbs_tab"
                                                                                    tabindex="-1" role="tab"><em
                                                                                        class="icon ni ni-cc-secure"></em><span>{{ __('DBS') }}</span></a>
                                                                        </li>
                                                                        <li class="nav-item d-none" role="presentation" id="tab-cek"><a
                                                                                    class="nav-link"
                                                                                    data-bs-toggle="tab"
                                                                                    href="#tabItem8"
                                                                                    aria-selected="false"
                                                                                    tabindex="-1" role="tab"><em
                                                                                        class="icon ni ni-cc-alt2"></em><span>{{ __('Check') }}</span></a>
                                                                        </li>
                                                                    </ul>
                                                                    <div class="tab-content">
                                                                        <div class="tab-pane active" id="tabItem5" role="tabpanel">
                                                                            <div class="row g-gs mb-3">
                                                                                <p>{{ __('You can pay for this order with a credit card or wire transfer.') }}
                                                                                    <a href="#" data-bs-target="#paymentHelp" data-bs-toggle="modal">{{ __('Help') }}</a>
                                                                                </p>
                                                                                <div class="col-md-7 col-lg-7">

                                                                                    <span class="preview-title overline-title">{{ __('Payment Amount') }}</span>
                                                                                    <div class="form-control-wrap mb-3">
                                                                                        <div class="form-text-hint">
                                                                                            <span class="overline-title">Try</span>
                                                                                        </div>
                                                                                        <input type="text" class="form-control" name="cart_total" id="cart_total" placeholder="{{ __('Amount to Pay') }}">
                                                                                    </div>

                                                                                    <span class="preview-title overline-title">{{ __('Bank Selection') }}</span>
                                                                                    <div class="form-control-wrap mb-3">
                                                                                        <div class="input-group">
                                                                                            <select class="form-control js-select2" name="bank" id="bank_id" required>
                                                                                                <option>{{ __('Select Bank') }}...</option>
                                                                                                @foreach($allPosServices as $bankKey => $bankConfig)
                                                                                                    @if(in_array($bankKey, $activePosServices))
                                                                                                        <option value="{{ $bankKey }}">{{ $bankConfig['name'] }}</option>
                                                                                                    @endif
                                                                                                @endforeach
                                                                                            </select>
                                                                                        </div>
                                                                                    </div>


                                                                                    <div class="form-group">
                                                                                        <span class="preview-title overline-title">{{ __('Card Information') }}</span>
                                                                                        <div class="form-control-wrap">
                                                                                            <div class="d-flex flex-wrap border border-light rounded">
                                                                                                <div class="w-100 border-bottom border-light d-flex align-items-center">
                                                                                                    <input class="form-control-plaintext px-3" type="text" name="name" id="cardHolder" autocomplete="off" placeholder="{{ __('Name on Card') }}">
                                                                                                </div>

                                                                                                <div class="w-100 border-bottom border-light d-flex align-items-center">
                                                                                                    <input class="form-control-plaintext px-3" type="number" id="cardNumber" name="number" placeholder="{{ __('Card Number') }}" minlength="13" autocomplete="off" maxlength="19">
                                                                                                    <ul class="d-flex pe-3 gx-1 flex-shrink-0">
                                                                                                        <li class="h-1rem d-inline-flex">
                                                                                                            <img src="/assets/images/icons/card/troy.png" alt="" class="h-100">
                                                                                                        </li>
                                                                                                        <li class="h-1rem d-inline-flex">
                                                                                                            <img src="/assets/images/icons/card/visa.png" alt="" class="h-100">
                                                                                                        </li>
                                                                                                        <li class="h-1rem d-inline-flex">
                                                                                                            <img src="/assets/images/icons/card/mastercard.png" alt="" class="h-100">
                                                                                                        </li>
                                                                                                    </ul>
                                                                                                </div>
                                                                                                <div class="w-50 border-end border-light">
                                                                                                    <input class="form-control-plaintext px-3 date-picker-ym" type="text" id="cardExpiry" name="monthyear" autocomplete="off" placeholder="{{ __('MM/YY Expiry') }}">
                                                                                                </div>
                                                                                                <div class="w-50"><input class="form-control-plaintext px-3" type="number" id="cardCvv" name="cvv" autocomplete="off" minlength="3" maxlength="4" placeholder="{{ __('CVV') }}">
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <input type="hidden" id="type" name="type" value="visa">
                                                                                    <input type="hidden" id="month" name="month">
                                                                                    <input type="hidden" id="year" name="year">
                                                                                    <input type="hidden" id="amount" value="" name="amount">
                                                                                    <input type="hidden" id="cart_id" value="" name="cart_id">
                                                                                    <button type="submit"
                                                                                            class="btn btn-block btn-primary"
                                                                                            id="btn-pay" disabled><span>{{ __('Pay') }}</span><em
                                                                                                class="icon ni sign-try-alt"></em>
                                                                                    </button>

                                                                                    <div class="credit-card mt-3 d-none" id="creditCard">
                                                                                        <div class="credit-card-inner">
                                                                                            <div class="credit-card-front">
                                                                                                <div class="d-flex justify-content-between align-items-center">
                                                                                                    <svg class="chip-image" viewBox="0 0 50 40" xmlns="http://www.w3.org/2000/svg">
                                                                                                        <rect x="5" y="5" width="40" height="30" rx="3" fill="#FFD700"/>
                                                                                                        <rect x="10" y="15" width="30" height="3" fill="#DAA520"/>
                                                                                                        <rect x="10" y="22" width="30" height="3" fill="#DAA520"/>
                                                                                                    </svg>
                                                                                                    <div class="card-logo" id="cardLogo">
                                                                                                        <!-- Default card logo (will be replaced by JS) -->
                                                                                                        <svg viewBox="0 0 65 40" xmlns="http://www.w3.org/2000/svg">
                                                                                                            <rect width="65" height="40" fill="none"/>
                                                                                                            <text x="5" y="25" fill="white" font-size="14">CARD</text>
                                                                                                        </svg>
                                                                                                    </div>
                                                                                                </div>
                                                                                                <div class="card-number" id="cardNumberDisplay">•••• •••• •••• ••••</div>
                                                                                                <div class="d-flex justify-content-between">
                                                                                                    <div>
                                                                                                        <div class="label">{{ __('Card Holder') }}</div>
                                                                                                        <div class="card-holder" id="cardHolderDisplay">{{ __('NAME SURNAME') }}</div>
                                                                                                    </div>
                                                                                                    <div>
                                                                                                        <div class="label">{{ __('Expiry') }}</div>
                                                                                                        <div class="card-expiry" id="cardExpiryDisplay">MM/YY</div>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="credit-card-back">
                                                                                                <div class="card-strip"></div>
                                                                                                <div class="card-cvv">
                                                                                                    <div class="label">CVV</div>
                                                                                                    <div id="cardCvvDisplay">•••</div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>

                                                                                </div>
                                                                                <div class="col-md-6 col-lg-5">

                                                                                    <span class="preview-title overline-title">{{ __('Installment Selection') }}</span>
                                                                                    <div id="installment_content">
                                                                                        <span class="placeholder col-12 placeholder-lg"></span>
                                                                                        <span class="placeholder col-12 placeholder-lg"></span>
                                                                                        <span class="placeholder col-12 placeholder-lg"></span>
                                                                                        <span class="placeholder col-12 placeholder-lg"></span>
                                                                                    </div>

                                                                                    <small class="text-center">{{ __('Additional installments are applied within bank campaigns and are not shown here.') }}</small>

                                                                                </div>

                                                                            </div>
                                                                        </div>
                                                                        <div class="tab-pane" id="tabItem6" role="tabpanel">
                                                                            <h5>{{ __('Our Account Numbers') }}</h5>
                                                                            <table class="table">
                                                                                <tbody>
                                                                                <tr>
                                                                                    <td>
                                                                                        <table>
                                                                                            <tbody>
                                                                                            <tr>
                                                                                                <td>
                                                                                                    <b>{{ __('ZIRAAT BANK TL ACCOUNT') }}</b><br>
                                                                                                    {{ __('AKDAĞ YALITIM A.Ş.') }}<br>

                                                                                                    TR 6500 0100 1781 9619 9439 5001
                                                                                                </td>
                                                                                            </tr>
                                                                                            </tbody>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                                </tbody>
                                                                            </table>
                                                                            <hr>
                                                                            <label for="eft_amt" class="form-label">{{ __('Amount Sent') }}</label>
                                                                            <div class="form-control-wrap">
                                                                                <div class="input-group">
                                                                                    <input type="number" min="0" step="any" class="form-control" required name="eft_amt" id="eft_amt" placeholder="{{ __('EFT Amount') }}">
                                                                                    <div class="input-group-append">
                                                                                        <button type="button" class="btn btn-primary btn-pay"   id="pay-with-eft-button">
                                                                                            {{ __('COMPLETE ORDER') }}
                                                                                        </button>
                                                                                    </div>
                                                                                </div>
                                                                            </div>

                                                                        </div>
                                                                        <div class="tab-pane" id="tabItem7" role="tabpanel">
                                                                            <p>{{ __('Your DBS contracts in the system.') }}</p>
                                                                            <div id="dbs_content"></div>
                                                                        </div>
                                                                        <div class="tab-pane" id="tabItem8" role="tabpanel">
                                                                            <p>{{ __('Check') }}</p>

                                                                            <table class="table">
                                                                                <thead>
                                                                                <tr>
                                                                                    <th>{{ __('Check No') }}</th>
                                                                                    <th>{{ __('Bank') }}</th>
                                                                                    <th>{{ __('Amount') }}</th>
                                                                                    <th>{{ __('Date') }}</th>
                                                                                    <th>{{ __('Due Date') }}</th>
                                                                                </tr>
                                                                                </thead>
                                                                                <tbody>
                                                                                <tr>
                                                                                    <td>123456</td>
                                                                                    <td>{{ __('Garanti Bank') }}</td>
                                                                                    <td>50.500,50</td>
                                                                                    <td>01.01.2022</td>
                                                                                    <td>01.01.2023</td>
                                                                                    <td>
                                                                                        <em class="icon ni ni-trash-empty"></em>
                                                                                    </td>
                                                                                </tr>
                                                                                </tbody>
                                                                            </table>


                                                                            <div class="btn-group mt-3 float-end"
                                                                                 id="cart_buttons">
                                                                                <button type="button"
                                                                                        class="btn btn-outline-primary btn-modal"
                                                                                        data-href="/cek/create"
                                                                                        data-container="#modal_container">
                                                                                    <span>{{ __('Add New Check') }}</span><em
                                                                                            class="icon ni ni-plus-circle-fill"></em>
                                                                                </button>
                                                                                <button type="button"
                                                                                        data-href="/delete-cart"
                                                                                        data-container="#modal_container"
                                                                                        class="btn btn-modal btn-block btn-outline-primary"
                                                                                        id="empty_list">
                                                                                    <span>{{ __('Clear List') }}</span><em
                                                                                            class="icon ni ni-trash-empty-fill"></em>
                                                                                </button>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="card-footer bg-light">
                                                                    {{--                                                                    <div class="custom-control custom-checkbox">--}}
                                                                    {{--                                                                        <input type="checkbox" class="custom-control-input" name="approveSalesContract" id="approveSalesContract" required>--}}
                                                                    {{--                                                                        <label class="custom-control-label" for="approveSalesContract">--}}
                                                                    {{--                                                                            <a href="" data-bs-target="#salesContractModal" data-bs-toggle="modal">{{ __('Distance Sales Contract') }}</a>--}}
                                                                    {{--                                                                        </label>--}}
                                                                    {{--                                                                    </div>--}}
                                                                </div>
                                                                <div class="card-body text-center" id="pay-with-contract-div">
                                                                    <p>{{ __('Click the complete order button to create your order.') }}</p>
                                                                    <button type="button" class="btn btn-lg btn-primary btn-pay" id="pay-with-contract-button">
                                                                        {{ __('COMPLETE ORDER') }}
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="nk-stepper-step text-center">
                                                            <h5 class="title mb-2">{{ __('Order Created!') }}</h5>
                                                            <p class="text-soft mb-3">{{ __('Your order entry process has been completed. You will be informed about the status of your order via email and the B2B portal.') }}</p>
                                                            <div class="gfx w-50 mx-auto">
                                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 114 113.9"><path d="M87.84,110.34l-48.31-7.86a3.55,3.55,0,0,1-3.1-4L48.63,29a3.66,3.66,0,0,1,4.29-2.8L101.24,34a3.56,3.56,0,0,1,3.09,4l-12.2,69.52A3.66,3.66,0,0,1,87.84,110.34Z" transform="translate(-4 -2.1)" fill="#c4cefe"></path><path d="M33.73,105.39,78.66,98.1a3.41,3.41,0,0,0,2.84-3.94L69.4,25.05a3.5,3.5,0,0,0-4-2.82L20.44,29.51a3.41,3.41,0,0,0-2.84,3.94l12.1,69.11A3.52,3.52,0,0,0,33.73,105.39Z" transform="translate(-4 -2.1)" fill="#c4cefe"></path><rect x="22" y="17.9" width="66" height="88" rx="3" ry="3" fill="#6576ff"></rect><rect x="31" y="85.9" width="48" height="10" rx="1.5" ry="1.5" fill="#fff"></rect><rect x="31" y="27.9" width="48" height="5" rx="1" ry="1" fill="#e3e7fe"></rect><rect x="31" y="37.9" width="23" height="3" rx="1" ry="1" fill="#c4cefe"></rect><rect x="59" y="37.9" width="20" height="3" rx="1" ry="1" fill="#c4cefe"></rect><rect x="31" y="45.9" width="23" height="3" rx="1" ry="1" fill="#c4cefe"></rect><rect x="59" y="45.9" width="20" height="3" rx="1" ry="1" fill="#c4cefe"></rect><rect x="31" y="52.9" width="48" height="3" rx="1" ry="1" fill="#e3e7fe"></rect><rect x="31" y="60.9" width="23" height="3" rx="1" ry="1" fill="#c4cefe"></rect><path d="M98.5,116a.5.5,0,0,1-.5-.5V114H96.5a.5.5,0,0,1,0-1H98v-1.5a.5.5,0,0,1,1,0V113h1.5a.5.5,0,0,1,0,1H99v1.5A.5.5,0,0,1,98.5,116Z" transform="translate(-4 -2.1)" fill="#9cabff"></path><path d="M16.5,85a.5.5,0,0,1-.5-.5V83H14.5a.5.5,0,0,1,0-1H16V80.5a.5.5,0,0,1,1,0V82h1.5a.5.5,0,0,1,0,1H17v1.5A.5.5,0,0,1,16.5,85Z" transform="translate(-4 -2.1)" fill="#9cabff"></path><path d="M7,13a3,3,0,1,1,3-3A3,3,0,0,1,7,13ZM7,8a2,2,0,1,0,2,2A2,2,0,0,0,7,8Z" transform="translate(-4 -2.1)" fill="#9cabff"></path><path d="M113.5,71a4.5,4.5,0,1,1,4.5-4.5A4.51,4.51,0,0,1,113.5,71Zm0-8a3.5,3.5,0,1,0,3.5,3.5A3.5,3.5,0,0,0,113.5,63Z" transform="translate(-4 -2.1)" fill="#9cabff"></path><path d="M107.66,7.05A5.66,5.66,0,0,0,103.57,3,47.45,47.45,0,0,0,85.48,3h0A5.66,5.66,0,0,0,81.4,7.06a47.51,47.51,0,0,0,0,18.1,5.67,5.67,0,0,0,4.08,4.07,47.57,47.57,0,0,0,9,.87,47.78,47.78,0,0,0,9.06-.87,5.66,5.66,0,0,0,4.08-4.09A47.45,47.45,0,0,0,107.66,7.05Z" transform="translate(-4 -2.1)" fill="#2ec98a"></path><path d="M100.66,12.81l-1.35,1.47c-1.9,2.06-3.88,4.21-5.77,6.3a1.29,1.29,0,0,1-1,.42h0a1.27,1.27,0,0,1-1-.42c-1.09-1.2-2.19-2.39-3.28-3.56a1.29,1.29,0,0,1,1.88-1.76c.78.84,1.57,1.68,2.35,2.54,1.6-1.76,3.25-3.55,4.83-5.27l1.35-1.46a1.29,1.29,0,0,1,1.9,1.74Z" transform="translate(-4 -2.1)" fill="#fff"></path></svg>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <hr>
                                                    <ul class="nk-stepper-pagination pt-4 gx-4 gy-2 stepper-pagination">
                                                        <li class="step-prev">
                                                            <button class="btn btn-dim btn-primary" id="btn-prev">{{ __('Previous') }}</button>
                                                        </li>
                                                        <li class="step-next">
                                                            <button class="btn btn-primary" id="btn-next">
                                                                <span>{{ __('Next') }}</span> <em
                                                                        class="icon ni ni-arrow-right"></em></button>
                                                        </li>
                                                        <li class="step-submit">
                                                            <a href="/siparis" class="btn btn-primary">{{ __('My Orders') }}</a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </form>
                            </div>
                        </div><!-- .nk-block -->
                    </div><!-- .components-preview -->
                </div>
            </div>
        </div>
    </div>

    <!-- content @e -->

    <!-- Modal Trigger Code -->
    <div class="modal modal-lg fade" tabindex="-1" id="paymentHelp">
        <div class="modal-dialog modal-dialog-scrollable" role="document">
            <div class="modal-content"><a href="#" class="close" data-dismiss="modal" aria-label="Close"> <em
                            class="icon ni ni-cross"></em> </a>
                <div class="modal-header"><h5 class="modal-title">{{ __('Payment Help') }}</h5></div>
                <div class="modal-body">
                    <ol class="">
                        <li>{{ __('To make a partial payment, enter the amount you want to pay with the relevant card in the "Payment Amount" field.') }}</li>
                        <li>{{ __('After the amount you entered is withdrawn, the "Payment Amount" field will be updated to reflect the remaining amount.') }}</li>
                        <li>{{ __('You can make the remaining payment in one go or by entering amounts with different cards.') }}</li>
                        <li>{{ __('Your total payments cannot exceed the order total.') }}</li>
                        <li>{{ __('To make your payment in one go, use the amount in the "Payment Amount" field.') }}</li>

                    </ol>

                </div>
            </div>
        </div>
    </div>

    @include('frontend.order.modal.sales_contract')
@endsection

@section('js')
    <script>

        const creditCard = document.getElementById('creditCard');
        const cardNumber = document.getElementById('cardNumber');
        const cardNumberDisplay = document.getElementById('cardNumberDisplay');
        const cardHolder = document.getElementById('cardHolder');
        const cardHolderDisplay = document.getElementById('cardHolderDisplay');
        const cardExpiry = document.getElementById('cardExpiry');
        const cardExpiryDisplay = document.getElementById('cardExpiryDisplay');
        const cardCvv = document.getElementById('cardCvv');
        const cardCvvDisplay = document.getElementById('cardCvvDisplay');
        const cardLogo = document.getElementById('cardLogo');


        // Sözleşme kalan tutarını saklamak için global değişken
        var contractRemainingAmount = 0;

        // Function to update cart badge - defined globally
        window.updateCartBadge = function(contractId) {
            // Check if cart badge element exists
            if (!$('#cart-badge').length) {
                console.log('Cart badge element not found');
                return;
            }
            
            if (!contractId || contractId == '0') {
                $('#cart-badge').hide();
                return;
            }
            
            $.ajax({
                url: '/get-cart-count',
                method: 'GET',
                data: { contract_id: contractId },
                success: function(response) {
                    console.log('Cart count response:', response);
                    if (response && response.count > 0) {
                        $('#cart-badge').text(response.count).show();
                    } else {
                        $('#cart-badge').hide();
                    }
                },
                error: function(xhr, status, error) {
                    console.log('Cart count error:', error);
                    $('#cart-badge').hide();
                }
            });
        };

        $('#contract_id').change(function () {
            var contractId = $(this).val();
            
            // Update cart badge
            window.updateCartBadge(contractId);
            
            $("#overlay").fadeIn(150);
            $.ajax({
                url: '/get-contract-summary',
                method: 'GET',
                data: {contract_id: contractId},
                success: function (response) {
                    $("#overlay").fadeOut(200);
                    if (response.success === true) {
                        $("#overlay").fadeOut(200);
                        $('#contract_content').html(response.html);
                        $('#step1_sub_title').html(response.doc_no);
                        $('#step3_sub_title').html(response.cart_count_msg);
                        $('#step5_sub_title').html(response.cart_total_amount);
                        $('#amount').val(response.amount);
                        $('#cart_id').val(response.cart_id);
                        $('#shipping_address_id').val(response.shipping_address_id);
                        $('#country_id').val(response.country_id);
                        $('#city_id').val(response.city_id);
                        $('#town_id').val(response.town_id);
                        $('#transport_type_select').val(response.incoterms_id);
                        $('#shipping_date').val(response.shipping_date);
                        $('#notes').val(response.notes);
                        // Set the end date of the datepicker; sevk tarihi sozlesme bitis tarihinden sonra olmamalı
                        $('.date-pickers').datepicker('setEndDate', response.endDate);
                        $('#product_quantity_content').find('input').val('');

                        // Sözleşme kalan tutarını sakla
                        contractRemainingAmount = parseFloat(response.remaining_amount) || 0;

                        NioApp.BS.progress('[data-progress]');
                        if (response.message) {
                            NioApp.Toast(response.message, 'success', {position: 'top-center'});
                        }
                        response.payment_tabs.forEach(function(tab) {
                            var tabElement = $('#tab-' + tab);
                            if (tabElement.length) {
                                tabElement.addClass('d-block').removeClass('d-none');
                            }
                        });
                    } else {
                        $("#overlay").fadeOut(100);
                        toastr.clear();
                        NioApp.Toast(response.message, 'error', {position: 'top-center', duration: 8000});
                    }
                }
            });
        });

        $(document).ready(function () {
            $("#approveSalesContract").click(function () {
                if ($(this).is(":checked")) {
                    $(".pay-with-dbs").attr("disabled", false);
                    $(".btn-pay").attr("disabled", false);
                    $('.approve-sales-tooltip').attr('data-bs-original-title', '');
                } else {
                    $(".pay-with-dbs").attr("disabled", true);
                    $(".btn-pay").attr("disabled", true);
                    $('.approve-sales-tooltip').attr('data-bs-original-title', 'Ödeme için Mesafeli Satış Sözleşmesi\'ni onaylayınız');
                }
            });

            $('#transport_type_select').change(function () {
                var addressId = $('#shipping_address_id').val();
                $.ajax({
                    url: '/get-shipping-price',
                    method: 'GET',
                    data: {address_id: addressId},
                    success: function (response) {
                        if (response.success === true) {
                            $("#shipping_price_input").val(response.value);
                        }
                    }
                });


                if ($(this).val() == 62) {
                    $('#shipping_price_div').addClass('d-block').removeClass('d-none');
                } else {
                    $('#shipping_price_div').removeClass('d-block').addClass('d-none');
                }
            });

            $('#contract_id').change(function () {
                if ($(this).val() == 0) {
                    $('#payment-types').addClass('d-block').removeClass('d-none');
                    $('#pay-with-contract-div').removeClass('d-block').addClass('d-none');
                } else {
                    $('#payment-types').removeClass('d-block').addClass('d-none');
                    $('#pay-with-contract-div').addClass('d-block').removeClass('d-none');
                }
            });

            $('#dbs_tab').on('click', function (e) {
                $("#overlay").fadeIn(150);
                $.ajax({
                    url: '/get-dbs',
                    method: 'GET',
                    success: function (response) {
                        $("#overlay").fadeOut(200);
                        if (response.success === true) {
                            $('#dbs_content').html(response.html);
                            $('[data-bs-toggle="tooltip"]').tooltip();
                            NioApp.BS.progress('[data-progress]');
                        } else {
                            $("#overlay").fadeOut(100);
                            $('#dbs_content').html(response.html);
                            toastr.clear();
                            NioApp.Toast(response.message, 'error', {position: 'top-center', duration: 8000});
                        }
                    }
                });
            });

            $('#btn-add-to-cart').on('click', function (e) {
                var productId = $('#product_id').val();
                var qty = $('#qty').val();
                var quantity_pallet = $('#quantity_pallet').val();
                var contractId = $('#contract_id').val();
                // Para birimi formatını temizle ve sayıya çevir
                var amtRaw = $('#amt').val() || '0';
                var amtVatRaw = $('#amt_vat').val() || '0';
                
                // Türk para formatından normal sayı formatına çevir (1.234,56 → 1234.56)
                var amt = parseFloat(amtRaw.replace(/[₺\s]/g, '').replace(/\./g, '').replace(',', '.')) || 0;
                var amt_vat = parseFloat(amtVatRaw.replace(/[₺\s]/g, '').replace(/\./g, '').replace(',', '.')) || 0;
                var vat_id = $('#vat_id').val();
                var unit_price_tra = $('#unit_price_tra').val();
                var covering_fee = $('#covering_fee').val();
                var price_list_id = $('#price_list_id').val();
                var ton_price = $('#ton_price').val();

                // Sözleşme seçilmişse ve sepet tutarı sözleşme kalan tutarından fazlaysa uyarı ver
                if (contractId && contractId > 0 && contractRemainingAmount > 0) {
                    // Mevcut sepet tutarını da Türk para formatından çevir
                    var currentCartTotalRaw = $('#amount').val() || '0';
                    var currentCartTotal = parseFloat(currentCartTotalRaw.toString().replace(/[₺\s]/g, '').replace(/\./g, '').replace(',', '.')) || 0;
                    var newItemTotal = amt + amt_vat;
                    var totalCartAmount = currentCartTotal + newItemTotal;

                    // if (totalCartAmount > contractRemainingAmount) {
                    //     NioApp.Toast('Uyarı: Sepet toplam tutarı (' + totalCartAmount.toFixed(2) + ' ₺) sözleşme kalan tutarından (' + contractRemainingAmount.toFixed(2) + ' ₺) fazla olamaz!', 'error', {position: 'top-center'});
                    //     return false;
                    // }
                }

                $("#overlay").fadeIn(150);
                $.ajax({
                    url: '/sepet',
                    method: 'POST',
                    data: {
                        "_token": "{{ csrf_token() }}",
                        product_id: productId,
                        amt: amt,
                        amt_vat: amt_vat,
                        price_list_id: price_list_id,
                        vat_id: vat_id,
                        ton_price: ton_price,
                        unit_price_tra: unit_price_tra,
                        covering_fee: covering_fee,
                        qty: qty,
                        quantity_pallet: quantity_pallet,
                        contract_id: contractId
                    },
                    success: function (response) {
                        $("#overlay").fadeOut(200);
                        if (response.success === true) {
                            toastr.clear();
                            NioApp.Toast(response.message, 'info', {position: 'top-right'});
                            $('#btn-add-to-cart').prop("disabled", true);
                            $('#step3_sub_title').html(response.cart_count_msg);
                            $('#step5_sub_title').html(response.cart_total_amount);
                            $('#amount').val(response.amount);
                            $('#cart_id').val(response.cart_id);
                            $('#installment_content').html('');
                            $("#bank_id").val("").change();
                            
                            // Update cart badge after successful add to cart
                            var contractId = $('#contract_id').val();
                            window.updateCartBadge(contractId);
                        } else {
                            $("#overlay").fadeOut(100);
                            toastr.clear();
                            NioApp.Toast(response.message, 'error', {position: 'top-center', duration: 8000});
                        }
                    },
                });
            });

            $('#pay-with-eft-button').on('click', function (e) {
                var contractId = $('#contract_id').val();
                var eft_amt = $('#eft_amt').val();
                $("#overlay").fadeIn(150);
                $.ajax({
                    url: '/pay-with-eft',
                    method: 'POST',
                    data: {
                        "_token": "{{ csrf_token() }}",
                        eft_amt: eft_amt,
                        contract_id: contractId
                    },
                    success: function (response) {
                        $("#overlay").fadeOut(200);
                        if (response.success === true) {
                            $("#btn-next").click();
                            toastr.clear();
                            NioApp.Toast(response.message, 'info', {position: 'top-right'});
                            $('#btn-add-to-cart').prop("disabled", true);
                            $('#step3_sub_title').html(response.cart_count_msg);
                            $('#step5_sub_title').html(response.cart_total_amount);
                            $('#amount').val(response.amount);
                            $('#cart_id').val(response.cart_id);
                            $('#installment_content').html('');
                            $("#bank_id").val("").change();
                        } else {
                            $("#overlay").fadeOut(100);
                            toastr.clear();
                            NioApp.Toast(response.message, 'error', {position: 'top-center', duration: 8000});
                        }
                    },
                });
            });

            $('#pay-with-contract-button').on('click', function (e) {
                var contractId = $('#contract_id').val();
                $("#overlay").fadeIn(150);
                $.ajax({
                    url: '/pay-with-contract',
                    method: 'POST',
                    data: {
                        "_token": "{{ csrf_token() }}",
                        contract_id: contractId
                    },
                    success: function (response) {
                        $("#overlay").fadeOut(200);
                        if (response.success === true) {
                            $("#btn-next").click();
                            toastr.clear();
                            NioApp.Toast(response.message, 'info', {position: 'top-right'});
                            $('#btn-add-to-cart').prop("disabled", true);
                            $('#step3_sub_title').html(response.cart_count_msg);
                            $('#step5_sub_title').html(response.cart_total_amount);
                            $('#amount').val(response.amount);
                            $('#cart_id').val(response.cart_id);
                            $('#installment_content').html('');
                            $("#bank_id").val("").change();
                        } else {
                            $("#overlay").fadeOut(100);
                            toastr.clear();
                            NioApp.Toast(response.message, 'error', {position: 'top-center', duration: 8000});
                        }
                    },
                });
            });

            $(document).on('click', '.btn-cart-remove', function (e) {
                var itemId = $(this).data('item-id');
                var cartId = $(this).data('cart-id');
                var contractId = $("#contract_id").val();
                $("#overlay").fadeIn(150);
                $.ajax({
                    url: '/delete-cart',
                    method: 'POST',
                    data: {
                        "_token": "{{ csrf_token() }}",
                        contract_id: contractId,
                        item_id: itemId,
                        cart_id: cartId
                    },
                    success: function (response) {
                        $("#overlay").fadeOut(200);
                        if (response.success === true) {
                            $('div#modal_container').modal('hide');
                            toastr.clear();
                            NioApp.Toast(response.message, 'info', {position: 'top-right'});
                            $('#cart_content').html(response.html);
                            $('#step3_sub_title').html(response.cart_count_msg);
                            $('#step5_sub_title').html(response.cart_total_amount);
                            $('#amount').val(response.amount);
                            $('#cart_id').val(response.cart_id);
                            $('#installment_content').html('');
                            $("#bank_id").val("").change();
                            if (response.cart_count === 0) {
                                $("#cart_buttons").children().prop('disabled', true);
                                $('#btn-next').prop("disabled", true);
                            } else {
                                $("#cart_buttons").children().prop('disabled', false);
                            }
                        } else {
                            $("#overlay").fadeOut(100);
                            toastr.clear();
                            NioApp.Toast(response.message, 'error', {position: 'top-center', duration: 8000});
                        }
                    },
                });
            });


            $('#btn-prev').on('click', function (e) {
                $('#btn-next').prop("disabled", false);
            });


            $('#category_id').change(function () {
                var category_id = $(this).val();
                var contract_id = $("#contract_id").val();
                $("#overlay").fadeIn(150);
                $.ajax({
                    url: '/get-products',
                    method: 'GET',
                    data: {category_id: category_id, contract_id: contract_id},
                    success: function (response) {
                        $("#overlay").fadeOut(200);
                        $('#density').html(response.densities);
                        $('#depth').html(response.depths);
                        $('#height').html(response.heights);
                        $('#width').html(response.widths);
                        populateSelectBox('#product_id', response.products);
                    }
                });
            });

            $('#density, #depth, #height, #width').change(function () {
                var category_id = $('#category_id').val();
                var contract_id = $("#contract_id").val();
                var density = $('#density').val();
                var depth = $('#depth').val();
                var height = $('#height').val();
                var width = $('#width').val();
                $("#overlay").fadeIn(150);
                $.ajax({
                    url: '/get-products',
                    method: 'GET',
                    data: {
                        category_id: category_id,
                        contract_id: contract_id,
                        density: density,
                        depth: depth,
                        height: height,
                        width: width,
                    },
                    success: function (response) {
                        $("#overlay").fadeOut(200);
                        $('#density').html(response.densities);
                        $('#depth').html(response.depths);
                        $('#height').html(response.heights);
                        $('#width').html(response.widths);
                        populateSelectBox('#product_id', response.products);

                    }
                });
            });

            function populateSelectBox(selector, options) {
                var selectBox = $(selector);
                selectBox.empty();
                selectBox.append($('<option>').val('').text('Seçiniz'));
                $.each(options, function (key, value) {
                    selectBox.append($('<option>').val(value).text(key));

                });
            }

            $('#product_id').change(function () {
                var productId = $(this).val();
                $("#overlay").fadeIn(150);
                $.ajax({
                    url: '/get-product',
                    method: 'GET',
                    data: {product_id: productId},
                    success: function (response) {
                        $("#overlay").fadeOut(200);
                        if (response.success === true) {
                            $('#product_content').html(response.html);
                            $('#product_quantity_content').find('input').val('');
                            $('#btn-add-to-cart').prop("disabled", true);
                            $('#btn-next').prop("disabled", true);
                            $('#quantity_truck').prop("disabled", false);
                            $('#qty').prop("disabled", false);
                            $('#quantity_pallet').prop("disabled", false);
                        } else {
                            $("#overlay").fadeOut(100);
                            toastr.clear();
                            NioApp.Toast(response.message, 'error', {position: 'top-center', duration: 8000});
                        }
                    },
                });
            });





            $(document).on('change', '#qty, #quantity_pallet, #quantity_truck', function (e) {
                var changedElementId = $(this).attr('id');
                var productId = $('#product_id').val();
                var quantityTruck = changedElementId === 'quantity_truck' ? $(this).val() : null;
                var qty = changedElementId === 'qty' ? $(this).val() : null;
                var quantityPallet = changedElementId === 'quantity_pallet' ? $(this).val() : null;
                var contract_id = $("#contract_id").val();
                $("#overlay").fadeIn(150);
                $.ajax({
                    url: '/get-product-price',
                    method: 'GET',
                    data: {
                        product_id: productId,
                        quantity_truck: quantityTruck,
                        quantity_pallet: quantityPallet,
                        qty: qty,
                        contract_id: contract_id
                    },
                    success: function (response) {
                        $("#overlay").fadeOut(200);
                        if (response.success === true) {
                            if (response.message) {
                                toastr.clear();
                                NioApp.Toast(response.message, 'info', {position: 'top-center'});
                            }

                            $('#product_quantity_content').html(response.html);
                            $('#unit_price_tra').html(response.unit_price_tra);
                            if (response.amount_total > 0) {
                                $('#btn-add-to-cart').prop("disabled", false);
                                $('#btn-next').prop("disabled", false);
                            }
                        } else {
                            $("#overlay").fadeOut(100);
                            toastr.clear();
                            NioApp.Toast(response.message, 'error', {position: 'top-center', duration: 8000});
                        }
                    }
                });

                if (changedElementId !== 'quantity_truck') {
                    $('#quantity_truck').val('');
                }
                if (changedElementId !== 'qty') {
                    $('#qty').val('');
                }
                if (changedElementId !== 'quantity_pallet') {
                    $('#quantity_pallet').val('');
                }
            });

            $(document).on('submit', "form#address_form", function (e) {
                e.preventDefault();
                var form = $(this);
                var data = form.serialize();
                $("#overlay").fadeIn(150);
                $.ajax({
                    method: 'POST',
                    url: $(this).attr('action'),
                    dataType: 'json',
                    data: data,
                    success: function (response) {
                        $("#overlay").fadeOut(200);
                        if (response.success === true) {
                            $('div#modal_container').modal('hide');
                            toastr.clear();
                            NioApp.Toast(response.message, 'info', {position: 'top-center'});
                            $('#shipping_address_id').empty();
                            $('#shipping_address_id').prepend($('<option>Adres seç</option>'));
                            $.each(response.addresses, function (key, value) {
                                $('#shipping_address_id').append('<option value="' + key + '">' + value + '</option>');
                            });
                        } else {
                            $("#overlay").fadeOut(100);
                            toastr.clear();
                            NioApp.Toast(response.message, 'error', {position: 'top-center', duration: 8000});
                        }
                    },
                });
            });
        });

        $('#modal_container').on('shown.bs.modal', function (e) {
            var country_select = $('#country_id');
            NioApp.Select2(country_select);
            country_select.select2({dropdownParent: $(this)});
            country_select.change(function () {
                var countryId = $(this).val();
                $("#overlay").fadeIn(150);
                $.ajax({
                    url: '/get-cities',
                    method: 'GET',
                    data: {country_id: countryId},
                    success: function (response) {
                        $("#overlay").fadeOut(200);
                        $('#city_id').empty();
                        $('#city_id').prepend($('<option>Şehir seç</option>'));
                        $.each(response, function (key, value) {
                            $('#city_id').append('<option value="' + key + '">' + value + '</option>');
                        });
                    }
                });
            })

            $('#copy_amt').on('click', function () {
                var amt = $(this).data('amt');
                $('input[name="amt"]').val(amt);
            });

            var city_select = $('#city_id');
            NioApp.Select2(city_select);
            city_select.select2({dropdownParent: $(this)});
            city_select.change(function () {

                var cityId = $(this).val();
                $("#overlay").fadeIn(150);
                $.ajax({
                    url: '/get-towns',
                    method: 'GET',
                    data: {city_id: cityId},
                    success: function (response) {
                        $("#overlay").fadeOut(200);
                        $('#town_id').empty();
                        $('#town_id').prepend($('<option>İlçe seç</option>'));
                        $.each(response, function (key, value) {
                            $('#town_id').append('<option value="' + key + '">' + value + '</option>');
                        });
                    }
                });
            });
        });

        $(document).on('submit', 'form#cheque_record_form', function (e) {
            e.preventDefault();
            var form = $("form#cheque_record_form")[0];
            var documents = form.documents;
            var data = new FormData(form);

            if (documents.length > 0) {
                data.append('documents', documents);
            }
            var url = $(this).attr("action");
            var method = $(this).attr("method");
            var ladda = Ladda.create(document.querySelector('.ladda-button'));
            ladda.start();
            $.ajax({
                method: method,
                url: url,
                data: data,
                dataType: "json",
                processData: false,
                contentType: false,
                success: function (result) {
                    ladda.stop();
                    if (result.success == true) {
                        $('div#modal_container').modal('hide');
                        toastr.success(result.msg);
                        cheque_records_table.ajax.reload(null, false);
                    } else {
                        toastr.error(result.msg);
                    }
                }
            });
        });

        $(document).on('submit', "form#pay_with_dbs_form", function (e) {
            e.preventDefault();
            var form = $(this);
            var data = form.serialize();
            $("#overlay").fadeIn(150);
            $.ajax({
                method: 'POST',
                url: $(this).attr('action'),
                dataType: 'json',
                data: data,
                success: function (response) {
                    $("#overlay").fadeOut(200);
                    if (response.success === true) {
                        $('div#modal_container').modal('hide');
                        Swal.fire("Ödeme Başarılı!", response.message, "success");
                        e.preventDefault();
                    } else {
                        $("#overlay").fadeOut(100);
                        toastr.clear();
                        NioApp.Toast(response.message, 'error', {position: 'top-center', duration: 8000});
                    }
                },
            });
        });

        $(document).ready(function() {
            // Update cart badge on page load if contract is selected
            var initialContractId = $('#contract_id').val();
            if (initialContractId) {
                window.updateCartBadge(initialContractId);
            }
            
            @isset($user_cart)
            // Load existing cart content
            $.ajax({
                url: "{{ route('sepet.index') }}",
                method: 'GET',
                data: {
                    contract_id: {{ $user_cart->contract_id }}
                },
                success: function(response) {
                    $('#cart_content').html(response.html);
                    $('#step3_sub_title').html(response.cart_count_msg);
                }
            });
            @endisset
            
            // Country change event to load cities
            $('#country_id').change(function() {
                var countryId = $(this).val();
                $("#overlay").fadeIn(150);
                $.ajax({
                    url: '/get-cities',
                    method: 'GET',
                    data: {country_id: countryId},
                    success: function(response) {
                        $("#overlay").fadeOut(200);
                        $('#city_id').empty();
                        $('#city_id').append('<option value="">{{ __("City") }}</option>');
                        $.each(response, function(key, value) {
                            $('#city_id').append('<option value="' + key + '">' + value + '</option>');
                        });
                        // Clear town dropdown when country changes
                        $('#town_id').empty();
                        $('#town_id').append('<option value="">{{ __("All") }}</option>');
                    }
                });
            });

            // City change event to load towns
            $('#city_id').change(function() {
                var cityId = $(this).val();
                $("#overlay").fadeIn(150);
                $.ajax({
                    url: '/get-towns',
                    method: 'GET',
                    data: {city_id: cityId},
                    success: function(response) {
                        $("#overlay").fadeOut(200);
                        $('#town_id').empty();
                        $('#town_id').append('<option value="">{{ __("All") }}</option>');
                        $.each(response, function(key, value) {
                            $('#town_id').append('<option value="' + key + '">' + value + '</option>');
                        });
                    }
                });
            });

            // Disable card information fields initially
            $('#cardNumber, #cardHolder, #cardExpiry, #cardCvv').prop('disabled', true);

            function validateForm() {
                var cardNumber = $('#cardNumber').val().replace(/\s+/g, '');
                var cardHolderName = $('#cardHolder').val().trim();
                var expDate = $('#cardExpiry').val().trim();
                var cvv = $('#cardCvv').val().trim();
                var bankId = $('#bank_id').val();

                var isCardNumberValid = cardNumber.length >= 13 && cardNumber.length <= 19;
                var isCardHolderNameValid = cardHolderName.length > 0;
                var isExpDateValid = /^(0[1-9]|1[0-2])\/\d{2}$/.test(expDate);
                var isCvvValid = cvv.length >= 3;
                var isBankValid = bankId.length > 0;

                if (isExpDateValid) {
                    var [month, year] = expDate.split('/');
                    $('input[name="month"]').val(month);
                    $('input[name="year"]').val('20' + year);
                }

                if (isCardNumberValid && isCardHolderNameValid && isExpDateValid && isCvvValid && isBankValid) {
                    $('#btn-pay').prop('disabled', false);
                } else {
                    $('#btn-pay').prop('disabled', true);
                }
            }

            $('#cardNumber, #cardHolder, #cardExpiry, #cardCvv').on('input', validateForm);



            @if(Request::get('action') == 'sepet')
            console.log('action')

            $('#btn-next').click();

            @endif
        });


        const cardLogos = {
            visa: `
                    <svg viewBox="0 0 65 40" xmlns="http://www.w3.org/2000/svg">
                        <path d="M0 15 L65 15 L60 25 L5 25 Z" fill="#fff"/>
                        <text x="5" y="35" fill="white" font-size="14" font-weight="bold">VISA</text>
                    </svg>
                `,
            mastercard: `
                    <svg viewBox="0 0 65 40" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="20" cy="20" r="12" fill="#EB001B"/>
                        <circle cx="35" cy="20" r="12" fill="#F79E1B" fill-opacity="0.8"/>
                        <text x="0" y="35" fill="white" font-size="10">MASTERCARD</text>
                    </svg>
                `,
            amex: `
                    <svg viewBox="0 0 65 40" xmlns="http://www.w3.org/2000/svg">
                        <rect width="65" height="30" fill="#27AEE3"/>
                        <text x="5" y="35" fill="white" font-size="12">AMEX</text>
                    </svg>
                `,
            discover: `
                    <svg viewBox="0 0 65 40" xmlns="http://www.w3.org/2000/svg">
                        <path d="M0 0 L65 0 L65 40 L0 40 Z" fill="#FF6600"/>
                        <text x="5" y="25" fill="white" font-size="12">DISCOVER</text>
                    </svg>
                `,
            default: `
                    <svg viewBox="0 0 65 40" xmlns="http://www.w3.org/2000/svg">
                        <rect width="65" height="40" fill="none"/>
                        <text x="5" y="25" fill="white" font-size="14">CARD</text>
                    </svg>
                `
        };

        // Kart tipini tespit etme fonksiyonu
        function detectCardType(number) {
            const cleanNumber = number.replace(/\D/g, '');

            if (cleanNumber.startsWith('4')) {
                return 'visa';
            } else if (/^5[1-5]/.test(cleanNumber)) {
                return 'mastercard';
            } else if (/^3[47]/.test(cleanNumber)) {
                return 'amex';
            } else if (/^6(?:011|5)/.test(cleanNumber)) {
                return 'discover';
            }

            return 'default';
        }

        $('#cardNumber').on('input', function() {
            var cardNumber = $(this).val().replace(/\s+/g, '');
            var contractId = $("#contract_id").val();
            var amount = $("#amount").val();
            if (cardNumber.length === 6 || cardNumber.length === 9 || cardNumber.length === 12 || cardNumber.length === 16) {
                $.ajax({
                    url: '/get-installment',
                    method: 'GET',
                    data: { card_number: cardNumber, contract_id: contractId, amount: amount },
                    success: function (response) {
                        $("#overlay").fadeOut(200);
                        if (response.success === true) {
                            $('#installment_content').html(response.html);
                        } else {
                            $("#overlay").fadeOut(100);
                            toastr.clear();
                            NioApp.Toast(response.message, 'error', {position: 'top-center', duration: 8000});
                        }
                    },
                    error: function(xhr) {
                        console.error(xhr);
                    }
                });
            }
        });

        cardNumber.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\s/g, '').replace(/\D/g, '');
            let formattedValue = '';

            for(let i = 0; i < value.length; i++) {
                if(i > 0 && i % 4 === 0) {
                    formattedValue += ' ';
                }
                formattedValue += value[i];
            }

            value = formattedValue;
            cardNumberDisplay.textContent = formattedValue || '•••• •••• •••• ••••';

            const cardType = detectCardType(value);
            $('input[name="type"]').val(cardType);

            console.log(value);
            cardLogo.innerHTML = cardLogos[cardType];

        });

        cardHolder.addEventListener('input', function(e) {
            let value = e.target.value.toUpperCase();
            cardHolderDisplay.textContent = value || 'AD SOYAD';
        });

        cardExpiry.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 2) {
                value = value.slice(0,2) + '/' + value.slice(2);
            }
            e.target.value = value;
            cardExpiryDisplay.textContent = value || 'MM/YY';
        });

        $('#cardExpiry').on('change', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 2) {
                value = value.slice(0, 2) + '/' + value.slice(2);
            }
            e.target.value = value;
            cardExpiryDisplay.textContent = value || 'MM/YY';
        });

        cardCvv.addEventListener('focus', function() {
            creditCard.classList.add('flipped');
        });

        cardCvv.addEventListener('blur', function() {
            creditCard.classList.remove('flipped');
        });


        cardCvv.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            e.target.value = value;
            cardCvvDisplay.textContent = value || '•••';
        });


    </script>
@endsection