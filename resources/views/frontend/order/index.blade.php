@extends('frontend.layouts.app')

@section('title') {{ __('Orders') }} @endsection

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            <div class="nk-block-head nk-block-head-sm">
                <div class="nk-block-between">
                    <div class="nk-block-head-content"><h3 class="nk-block-title page-title">{{ __('All Orders') }}</h3></div>
                    <div class="nk-block-head-content">
                        <div class="toggle-wrap nk-block-tools-toggle"><a href="#"
                                                                          class="btn btn-icon btn-trigger toggle-expand me-n1"
                                                                          data-target="pageMenu"><em
                                        class="icon ni ni-more-v"></em></a>
                            <div class="toggle-expand-content" data-content="pageMenu">
                                <ul class="nk-block-tools g-3">

                                    <li>
                                        <div class="form-control-wrap">
                                            <div class="form-icon form-icon-right"><em class="icon ni ni-search"></em>
                                            </div>
                                            <input type="text" class="form-control" id="default-04"
                                                   placeholder="{{ __('Search by document number') }}"></div>
                                    </li>
                                    <li><a href="/siparis/rapor" class="btn btn-white btn-dim btn-outline-light"><em class="icon ni ni-reports"></em><span>{{ __('Order Report') }}</span></a></li>

                                    <li>
                                        <a href="{{ route('order.shipment_report')}}" class="btn btn-icon btn-dim btn-secondary d-md-none">
                                            <em class="icon ni ni-truck"></em></a>
                                        <a href="{{ route('order.shipment_report')}}" class="btn btn-dim btn-secondary d-none d-md-inline-flex"><em class="icon ni ni-truck"></em><span>{{ __('Order Shipment Report') }}</span>
                                        </a>
                                    </li>
                                    <li class="nk-block-tools-opt"><a href="{{ route('order.create_wizard')}}" class="btn btn-icon btn-primary d-md-none"><em
                                                    class="icon ni ni-plus"></em></a><a href="{{ route('order.create_wizard')}}"
                                                                                        class="btn btn-primary d-none d-md-inline-flex"><em
                                                    class="icon ni ni-plus"></em><span>{{ __('Create Order') }}</span></a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="accordion-2" class="accordion accordion-s3 p-1 mb-3">
                <div class="accordion-item"><a href="#" class="accordion-head" data-bs-toggle="collapse"
                                               data-bs-target="#accordion-item-2-1"><h6 class="title">{{ __('Filters') }}</h6>
                        <span class="accordion-icon"></span> </a>
                    <div class="accordion-body collapse" id="accordion-item-2-1" data-bs-parent="#accordion-2">
                        <div class="card">
                            <div class="card-inner">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="entity_id" class="form-label">{{ __('Company') }}</label>
                                            <select name="entity_id" id="entity_id" class="form-select js-select2" data-search="on" style="width:100%">
                                                <option value="">{{ __('All') }}</option>
                                                @foreach($entities as $id => $name)
                                                    <option value="{{ $id }}">{{ $name }}</option>
                                                @endforeach
                                            </select>
                                        </div>

                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group"><label class="form-label">{{ __('Date') }}</label>
                                            <div class="form-control-wrap">
                                                <div class="input-daterange date-picker-range input-group">
                                                    <input type="text" autocomplete="off" name="start_date" id="start_date" class="form-control" value="1.1.{{ date('Y', time()) }}"/>
                                                    <div class="input-group-addon">-</div>
                                                    <input type="text" autocomplete="off" name="end_date" id="end_date" class="form-control" value="31.12.{{ date('Y', time()) }}"/></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="form-label">{{ __('Order Status') }}</label>
                                            <div class="form-control-wrap">
                                                <select class="form-control" name="order_status" id="order_status">
                                                    <option value="">{{ __('All') }}</option>
                                                    <option value="1" selected>{{ __('Open') }}</option>
                                                    <option value="2">{{ __('Closed') }}</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="form-label">{{ __('Contract Status') }}</label>
                                            <div class="form-control-wrap">
                                                <select class="form-control" name="contract_status" id="contract_status">
                                                    <option value="">{{ __('All') }}</option>
                                                    <option value="contracted">{{ __('Contracted Sales') }}</option>
                                                    <option value="no-contract">{{ __('Non-Contracted Sales') }}</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="card card-bordered card-{{ __('Preview') }}">


                <div class="card card-bordered card-{{ __('Preview') }}">
                    <div class="card-body">
                        <table class="nk-tb-list nk-tb-ulist" id="data_table" style="width:100%">
                            <thead>
                            <tr class="nk-tb-item nk-tb-head">
                                <th class="nk-tb-col">
                                    #
                                </th>
                                <th class="nk-tb-col"><span class="sub-text">{{ __('Order Number') }}</span></th>
                                <th class="nk-tb-col"><span class="sub-text">{{ __('Company Title') }}</span></th>
                                <th class="nk-tb-col"><span class="sub-text">{{ __('Contract') }}</span></th>
                                <th class="nk-tb-col"><span class="sub-text">{{ __('Order Date') }}</span></th>
                                <th class="nk-tb-col"><span class="sub-text">{{ __('Order Amount') }}</span></th>
                                <th class="nk-tb-col"><span class="sub-text">{{ __('Payment Method') }}</span></th>
                                <th class="nk-tb-col"><span class="sub-text">{{ __('Action') }}</span></th>
                            </tr>
                            </thead>

                        </table>
                    </div>

                </div>
                <!-- end tab content -->
            </div><!-- .card-{{ __('Preview') }} -->

            <!-- end row -->
        </div>
        <!-- container-fluid -->
    </div>
@endsection

@section('js')
    <script type="text/javascript">
        $(document).on('submit', 'form#offer_approve_form', function (e) {
            url = $(this).attr('action');
            var method = $(this).attr("method");
            var payment_method_id =
                e.preventDefault();
            $("#overlay").fadeIn(150);
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            $.ajax({
                method: method,
                url: url,
                data: {
                    payment_method_id: $('select[name="payment_method_id"]').val(),
                },
                dataType: "json",
                success: function (result) {
                    $("#overlay").fadeOut(200);
                    if (result.success == true) {
                        $('div#modal_container').modal('hide');
                        Swal.fire(result.message, '', 'success')
                        //NioApp.Toast(result.msg, 'info', {position: 'top-right'});
                    } else {
                        toastr.clear();
                        NioApp.Toast(result.msg, 'error', {position: 'top-right'});
                    }
                }
            });
        });

        $(document).on('submit', "form#cancel_request_form", function (e) {
            e.preventDefault();
            var form = $(this);
            var data = form.serialize();
            var cancel_request_form_submit = $('#cancel_request_form_submit');
            cancel_request_form_submit.prop('disabled', true);
            $("#overlay").fadeIn(150);
            $.ajax({
                method: 'POST',
                url: $(this).attr('action'),
                dataType: 'json',
                data: data,
                success: function (response) {
                    $("#overlay").fadeOut(200);
                    if (response.success === true) {
                        $('div#modal_container').modal('hide');
                        toastr.clear();
                        NioApp.Toast(response.message, 'info', {position: 'top-center'});

                    } else {
                        $("#overlay").fadeOut(100);
                        toastr.clear();
                        NioApp.Toast(response.message, 'error', {position: 'top-center'});
                    }
                },
            });
        });


        $(document).ready( function(){
            has_export = true;
            var export_title = $(this).data('export-title') ? $(this).data('export-title') : 'Export';
            var btn = has_export ? '<"dt-export-buttons d-flex align-center"<"dt-export-title d-none d-md-inline-block">B>' : '',
                btn_cls = has_export ? ' with-export' : '';
            var dom = '<"row justify-between g-2' + btn_cls + '"<"col-7 col-sm-4 text-start"f><"col-5 col-sm-8 text-end"<"datatable-filter"<"d-flex justify-content-end g-2"' + btn + 'l>>>><"datatable-wrap my-3"t><"row align-items-center"<"col-7 col-sm-12 col-md-9"p><"col-5 col-sm-12 col-md-3 text-start text-md-end"i>>';
            var data_table = $('#data_table').DataTable({
                processing: true,
                serverSide: true,
                responsive: false,
                ajax: {
                    url: '{{ action('App\Http\Controllers\Frontend\OrderController@index') }}',
                    data: function (d) {
                        d.entity_id = $('select#entity_id').val();
                        d.order_status = $('select#order_status').val();
                        d.contract_status = $('select#contract_status').val();
                        d.start_date = $('input#start_date')
                            .val()
                        ;

                        d.end_date = $('input#end_date')
                            .val()

                        ;
                    },
                },

                scrollCollapse: true,
                scroller: true,
                scrollY: 600,

                columnDefs: [ {
                    "targets": [1],
                    "orderable": true,
                    "searchable": true
                } ],

                dom: dom,

                buttons: ['copy', 'excel', 'colvis'],
                createdRow: (row, data, dataIndex, cells) => {
                    $( row ).find('td').addClass('nk-tb-col');
                    $(row).addClass("nk-tb-item");
                },
                language: {
                    search: "",
                    searchPlaceholder: "{{ __('Search') }}",
                    lengthMenu: "<span class='d-none d-sm-inline-block'>{{ __('Show') }}</span><div class='form-control-select'> _MENU_ </div>",
                    info: "_START_ -_END_ {{ __('total') }} _TOTAL_",
                    infoEmpty: "0",
                    infoFiltered: "( {{ __('Total') }} _MAX_  )",
                    paginate: {
                        "first": "{{ __('First') }}",
                        "last": "{{ __('Last') }}",
                        "next": "{{ __('Next') }}",
                        "previous": "{{ __('Previous') }}"
                    }
                },

                aaSorting: [[0, 'desc']],
                columns: [
                    { data: 'id', name: 'id' },
                    { data: 'doc_no', name: 'doc_no', orderable: false, searchable: true },
                    { data: 'entity_name', name: 'entity_name', orderable: false, searchable: false },
                    { data: 'contract_no', name: 'contract_no', orderable: false, searchable: false },
                    { data: 'doc_date', name: 'doc_date', orderable: false, searchable: false },
                    { data: 'amt', name: 'amt' },
                    { data: 'payment_method', name: 'payment_method', orderable: false, searchable: false  },
                    { data: 'action', name: 'action', orderable: false, searchable: false },
                ],createdRow: (row, data, dataIndex, cells) => {

                    $(row).find('td:eq(0)').prepend('<i style="margin:auto;" class="ni ni-plus-c text-primary no-Aktar order-details" title="{{ __('Show Order Details') }}"></i>&nbsp;');
                    $( row ).find('td').addClass('nk-tb-col');
                    $(row).addClass("nk-tb-item");
                    $(row).css('background-color', data.status_color)
                },

            });

            var detailRows = [];
            $('#data_table tbody').on( 'click', 'tr i.order-details', function () {
                var i = $(this);
                var tr = $(this).closest('tr');
                var row = data_table.row( tr );
                var idx = $.inArray( tr.attr('id'), detailRows );

                if ( row.child.isShown() ) {
                    i.addClass('ni-plus-c text-primary');
                    i.removeClass('ni-minus-c text-danger');
                    row.child.hide();
                    detailRows.splice( idx, 1 );
                } else {
                    i.removeClass('ni-plus-c text-primary');
                    i.addClass('ni-minus-c text-danger');
                    row.child( get_product_details( row.data() ) ).show();
                    // Add to the 'open' array
                    if ( idx === -1 ) {
                        detailRows.push( tr.attr('id') );
                    }
                }
            });


            function get_product_details(rowData) {
                var div = $('<div/>')
                    .addClass('text-center')
                    .text('{{ __('Loading...') }}');

                $.ajax({
                    dataType: 'html',
                    url: '/siparis/' + rowData.id + '/detay',
                    success: function(data) {
                        div.html(data).removeClass('loading');

                    },
                });
                return div;
            }

            $('body').tooltip({selector: '[data-bs-toggle="tooltip"]'});
            $('select#entity_id').on(
                'change',
                function () {
                    data_table.ajax.reload(null, false);
                }
            );

            $('select#order_status').on(
                'change',
                function () {
                    data_table.ajax.reload(null, false);
                }
            );
            $('select#contract_status').on(
                'change',
                function () {
                    data_table.ajax.reload(null, false);
                }
            );

            $('input#start_date').on('change', function () {
                    data_table.ajax.reload(null, false);
                }
            );
            $('input#end_date').on('change', function () {
                    data_table.ajax.reload(null, false);
                }
            );


        });

    </script>
    <script>
        $(document).ready(function() {
            $('.btn-detail').click(function() {
                var orderId = $(this).data('id');
                $('#order' + orderId).css("display","block")
                $.ajax({
                    url: '/siparis/detay',
                    method: 'GET',
                    data: { order_id: orderId },
                    success: function(response) {
                        $('#order' + orderId).html(response);
                        NioApp.DataTable('.datatable-init', {
                            responsive: {
                                details: true
                            }
                        });
                    }
                });
            });
        });

        $(document).ready(function () {
            $(document).on('click', '.btn-modal', function (e) {
                e.preventDefault();
                var container = $(this).data('container');
                var url = $(this).data('href');

                $.ajax({
                    url: url,
                    dataType: 'html',
                    success: function (result) {
                        $(container)
                            .html(result)
                            .modal('show');

                        NioApp.DataTable('.datatable-init-export', {
                            responsive: {
                                details: true
                            },
                            buttons: ['copy', 'excel', 'csv', 'pdf', 'colvis']
                        });


                    },
                });
            });
        });
    </script>
@endsection