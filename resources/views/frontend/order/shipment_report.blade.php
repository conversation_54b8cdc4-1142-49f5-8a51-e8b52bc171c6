@extends('frontend.layouts.app')

@section('title') {{ __('Dispatch Report') }} @endsection

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            <div class="nk-block-head nk-block-head-sm">
                <div class="nk-block-between">
                    <div class="nk-block-head-content"><h3 class="nk-block-title page-title">{{ __('Dispatch Report') }}</h3></div>
                    <div class="nk-block-head-content">
                        <div class="toggle-wrap nk-block-tools-toggle"><a href="#"
                                                                          class="btn btn-icon btn-trigger toggle-expand me-n1"
                                                                          data-target="pageMenu"><em
                                        class="icon ni ni-more-v"></em></a>
                            <div class="toggle-expand-content" data-content="pageMenu">
                                <ul class="nk-block-tools g-3">
                                    <li class="nk-block-tools-opt"><a href="/siparis/rapor" class="btn btn-white btn-dim btn-outline-light"><em class="icon ni ni-reports"></em><span>{{ __('Order Report') }}</span></a></li>

                                    <li>
                                        <a href="/siparis/olustur" class="dropdown-toggle btn btn-primary"><em class="d-none d-sm-inline icon ni ni-plus-circle"></em><span><span class="d-none d-md-inline">{{ __('Order') }}</span> {{ __('Add') }}</span><em class="dd-indc icon ni ni-chevron-right"></em></a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-inner">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="entity_id" class="form-label">{{ __('Current Account') }}</label>
                                <select name="entity_id" id="entity_id" class="form-select js-select2" data-search="on" style="width:100%">
                                    <option value="">{{ __('All') }}</option>
                                    @foreach($entities as $id => $name)
                                        <option value="{{ $id }}">{{ $name }}</option>
                                    @endforeach
                                </select>
                            </div>

                        </div>
                        <div class="col-md-4">
                            <div class="form-group"><label class="form-label">{{ __('Date') }}</label>
                                <div class="form-control-wrap">
                                    <div class="input-daterange date-picker-range input-group">
                                        <input type="text" autocomplete="off" name="start_date" id="start_date" class="form-control"/>
                                        <div class="input-group-addon">-</div>
                                        <input type="text" autocomplete="off" name="end_date" id="end_date" class="form-control"/></div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>


            <div class="card card-bordered card-preGör">
                <div class="card-body">
                    <table class="nk-tb-list nk-tb-ulist" id="data_table" style="width:100%">
                        <thead>
                        <tr class="nk-tb-item nk-tb-head">
                            <th class="nk-tb-col"><span class="sub-text text-uppercase">{{ __('Order Date') }}</span></th>
                            <th class="nk-tb-col"><span class="sub-text text-uppercase">{{ __('Order Number') }}</span></th>
                            <th class="nk-tb-col"><span class="sub-text text-uppercase">{{ __('Contract Code') }}</span></th>
                            <th class="nk-tb-col"><span class="sub-text text-uppercase">{{ __('Current Account Name') }}</span></th>

                            <th class="nk-tb-col"><span class="sub-text text-uppercase">{{ __('Order Quantity') }}</span></th>
                            <th class="nk-tb-col"><span class="sub-text text-uppercase">{{ __('Dispatch Quantity') }}</span></th>
                            <th class="nk-tb-col"><span class="sub-text text-uppercase">{{ __('Remaining Quantity') }}</span></th>
                            <th class="nk-tb-col"><span class="sub-text text-uppercase">{{ __('Pallet') }}</span></th>
                            <th class="nk-tb-col"><span class="sub-text text-uppercase">{{ __('Description') }}</span></th>
                        </tr>
                        </thead>

                    </table>
                </div>

            </div><!-- .card-preGör -->

            <!-- end row -->
        </div>
        <!-- container-fluid -->
    </div>
@endsection

@section('js')
    <script type="text/javascript">
        $(document).on('submit', 'form#offer_approve_form', function (e) {
            url = $(this).attr('action');
            var method = $(this).attr("method");
            var payment_method_id =
                e.preventDefault();
            $("#overlay").fadeIn(150);
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            $.ajax({
                method: method,
                url: url,
                data: {
                    payment_method_id: $('select[name="payment_method_id"]').val(),
                },
                dataType: "json",
                success: function (result) {
                    $("#overlay").fadeOut(200);
                    if (result.success == true) {
                        $('div#modal_container').modal('hide');
                        Swal.fire(result.message, '', 'success')
                        //NioApp.Toast(result.msg, 'info', {position: 'top-right'});
                    } else {
                        toastr.clear();
                        NioApp.Toast(result.msg, 'error', {position: 'top-right'});
                    }
                }
            });

        });
        $(document).ready( function(){
            var dom = '<"row justify-between g-2 has_export"<"col-7 col-sm-4 text-start"f><"col-5 col-sm-8 text-end"<"datatable-filter"<"d-flex justify-content-end g-2" l>>>><"my-3"t><"row align-items-center"<"col-7 col-sm-12 col-md-9"p><"col-5 col-sm-12 col-md-3 text-start text-md-end"i>>';
            var data_table = $('#data_table').DataTable({
                processing: true,
                serverSide: true,
                responsive: false,

                ajax: {
                    url: '{{ action('App\Http\Controllers\Frontend\OrderController@shipment_report') }}',
                    data: function (d) {
                        d.entity_id = $('select#entity_id').val();

                        d.start_date = $('input#start_date')
                            .val()
                        ;
                        d.end_date = $('input#end_date')
                            .val()
                        ;
                    },
                },

                scrollCollapse: true,
                scroller: true,
              //  scrollY: 600,

                columnDefs: [ {
                    "targets": [1],
                    "orderable": true,
                    "searchable": true
                } ],

                dom: dom,
                createdRow: (row, data, dataIndex, cells) => {
                    $( row ).find('td').addClass('nk-tb-col');
                    $(row).addClass("nk-tb-item");
                },
                language: {
                    search: "",
                    searchPlaceholder: "Arama yap",
                    lengthMenu: "<span class='d-none d-sm-inline-block'>Göster</span><div class='form-control-select'> _MENU_ </div>",
                    info: "_START_ -_END_ toplam _TOTAL_",
                    infoEmpty: "0",
                    infoFiltered: "( Toplam _MAX_  )",
                    paginate: {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    }
                },

                aaSorting: [[0, 'desc']],
                columns: [
                    { data: 'doc_date', name: 'doc_date' },
                    { data: 'doc_no', name: 'doc_no', orderable: false, searchable: true },
                    { data: 'contract_no', name: 'contract_no', orderable: false, searchable: false },
                    { data: 'entity_name', name: 'entity_name', orderable: false, searchable: false },

                    { data: 'qty', name: 'qty', orderable: false, searchable: false },
                    { data: 'qty_shipping', name: 'qty_shipping', orderable: false, searchable: false },
                    { data: 'remaining_amount', name: 'remaining_amount', orderable: false, searchable: false  },
                    { data: 'zz_qty2', name: 'zz_qty2', orderable: false, searchable: false  },
                    { data: 'note3', name: 'note3', orderable: false, searchable: true },
                ],createdRow: (row, data, dataIndex, cells) => {
                    $(row).find('td:eq(0)').prepend('<i style="margin:auto;" class="ni ni-plus-c text-primary no-Aktar order-details" title="Sipariş Detayını Göster"></i>&nbsp;');
                    $( row ).find('td').addClass('nk-tb-col');
                    $(row).addClass("nk-tb-item");
                },

            });


            var detailRows = [];
            $('#data_table tbody').on( 'click', 'tr i.order-details', function () {
                var i = $(this);
                var tr = $(this).closest('tr');
                var row = data_table.row( tr );
                var idx = $.inArray( tr.attr('id'), detailRows );

                if ( row.child.isShown() ) {
                    i.addClass('ni-plus-c text-primary');
                    i.removeClass('ni-minus-c text-danger');
                    row.child.hide();
                    detailRows.splice( idx, 1 );
                } else {
                    i.removeClass('ni-plus-c text-primary');
                    i.addClass('ni-minus-c text-danger');
                    row.child( get_product_details( row.data() ) ).show();
                    // Add to the 'open' array
                    if ( idx === -1 ) {
                        detailRows.push( tr.attr('id') );
                    }
                }
            });


            function get_product_details(rowData) {
                var div = $('<div/>')
                    .addClass('text-center')
                    .text('{{ __('Loading...') }}');

                $.ajax({
                    dataType: 'html',
                    url: '/siparis/' + rowData.id + '/detay',
                    success: function(data) {
                        div.html(data).removeClass('loading');

                    },
                });
                return div;
            }

            $('body').tooltip({selector: '[data-bs-toggle="tooltip"]'});
            $('select#entity_id').on(
                'change',
                function () {
                    data_table.ajax.reload(null, false);
                }
            );

            $('select#order_status').on(
                'change',
                function () {
                    data_table.ajax.reload(null, false);
                }
            );
            $('select#contract_status').on(
                'change',
                function () {
                    data_table.ajax.reload(null, false);
                }
            );

            $('input#start_date').on('change', function () {
                    data_table.ajax.reload(null, false);
                }
            );
            $('input#end_date').on('change', function () {
                    data_table.ajax.reload(null, false);
                }
            );


        });

    </script>
    <script>
        $(document).ready(function() {
            $('.btn-detail').click(function() {
                var orderId = $(this).data('id');
                $('#order' + orderId).css("display","block")
                $.ajax({
                    url: '/siparis/detay',
                    method: 'GET',
                    data: { order_id: orderId },
                    success: function(response) {
                        $('#order' + orderId).html(response);
                        NioApp.DataTable('.datatable-init', {
                            responsive: {
                                details: true
                            }
                        });
                    }
                });
            });
        });

        $(document).ready(function () {
            $(document).on('click', '.btn-modal', function (e) {
                e.preventDefault();
                var container = $(this).data('container');
                var url = $(this).data('href');

                $.ajax({
                    url: url,
                    dataType: 'html',
                    success: function (result) {
                        $(container)
                            .html(result)
                           .modal('show');

                        NioApp.DataTable('.datatable-init-export', {
                            responsive: {
                                details: true
                            },
                            buttons: ['copy', 'excel', 'csv', 'pdf', 'colvis']
                        });


                    },
                });
            });
        });
    </script>
@endsection