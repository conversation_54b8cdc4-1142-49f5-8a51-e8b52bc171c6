<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\Entity;
use App\Models\PriceList;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Laracasts\Flash\Flash;

class SettingController extends Controller
{



    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {

        $section = request()->get('bolum', null);
        $data['section'] = $section;

        if(empty($section)) {

            $data['title'] = 'Ayarları düzenle';
            $data['settings'] = Setting::paginate();

            return view("backend.settings.index", compact("data",));

        }  elseif($section == 'varsayilan-fiyat-listesi') {

            $data['title'] = 'Varsayılan Fiyat Listesi';
            $data['price_lists'] = PriceList::orderByDesc('id')->where('ispassive',0)->take(20)->pluck('description_1 as name', 'id');
            $data['default_price_list_id'] = PriceList::where('is_default',1)->value('id');

            return view("backend.settings.default_price_list", $data);

        } elseif($section == 'pos-settings') {

            $data['default_pos'] = Setting::where('name', 'default_pos')->value('val');
            $data['title'] = 'POS Ayarları';
            $data['banks'] = config('laravel-pos.banks');

            return view("backend.settings.default_pos", $data);

        }  elseif($section == 'siparis-miktarlari') {

//            $company = Company::where('is_default', 1)->first();
//            $entities = $company->entities()->select('id','entity_name', 'order_quantity')->get();
            $entities = Entity::whereNotNull('is_default')->select('id','entity_name', 'order_quantity')->get();

            $title = 'Sipariş Miktarları';
            $sub_title = 'Serbest miktar girişi yapabilir mi?';
            $ayarlar = Setting::paginate();

            Log::info(label_case('Ayarlar') . ' | Kullanıcı:' . Auth::user()->name . '(ID:' . Auth::user()->id . ')');

            return view("backend.settings.order_quantity", compact('title', 'entities', 'ayarlar', 'sub_title'));

        } elseif($section == 'pos-taksit-sayilari') {

            $data['title'] = 'Taksit Bilgisi';
            $pos_installments = Setting::select('name','val')->where('name', 'like', '%taksit_sayisi_%')->get()->toArray();

            $data['pos_installments'] = $pos_installments;

            return view("backend.settings.default_instalment", $data);

        } elseif($section == 'veri-guncelle') {

            $data['title'] = 'Veri Güncelle';
            $data['artisans'] = [
                [
                    'name' => 'Siparişleri Güncelle',
                    'command' => 'get:orders',
                    'duration' => '5-10 dakika',
                ],
                [
                    'name' => ' Cari Bilgileri Güncelle',
                    'command' => 'get:entities',
                    'duration' => '1 dakika',
                ],
                          [
                    'name' => 'Firma Cari Güncelle',
                    'command' => 'get:company-entity',
                    'duration' => '1-2 dakika',
                ],
                [
                    'name' => 'Fiyatları Güncelle',
                    'command' => 'get:prices',
                    'duration' => '5-10 dakika',
                ],             [
                    'name' => 'Fiyat Listesi Güncelle',
                    'command' => 'get:price-lists',
                    'duration' => '3-5 dakika',
                ],
                [
                    'name' => 'Satış Temsilcileri Güncelle',
                    'command' => 'get:sales-persons',
                    'duration' => '1 dakika',
                ],
                [
                    'name' => 'Stok Kartı Güncelle',
                    'command' => 'get:products',
                    'duration' => '30-40 dakika',
                ],
                [
                    'name' => 'Cari Stok Kartı Güncelle',
                    'command' => 'get:product-branch',
                    'duration' => '8-12 dakika',
                ],
                [
                    'name' => 'Teminat Mektubu Güncelle',
                    'command' => 'get:letter-credits',
                    'duration' => '2-3 dakika',
                ],
                [
                    'name' => 'Teklif Güncelle',
                    'command' => 'get:offers',
                    'duration' => '5-8 dakika',
                ],
                [
                    'name' => 'İrsaliye Güncelle',
                    'command' => 'get:waybills',
                    'duration' => '6-10 dakika',
                ],
                [
                    'name' => 'DBS Güncelle',
                    'command' => 'get:dbs',
                    'duration' => '3-5 dakika',
                ],             [
                    'name' => 'Ülke Listesi Güncelle',
                    'command' => 'get:countries',
                    'duration' => '1 dakika',
                ],             [
                    'name' => 'Şehir Listesi Güncelle',
                    'command' => 'get:cities',
                    'duration' => '1-2 dakika',
                ],             [
                    'name' => 'İlçe Listesi Güncelle',
                    'command' => 'get:towns',
                    'duration' => '2-3 dakika',
                ],
            ];

            return view("backend.settings.sync_data", $data);

        } elseif($section == 'popup-ayarlari') {

            $data['title'] = 'Popup Ayarları';
            
            $popup_excluded_entities = Setting::where('name', 'popup_excluded_entities')->value('val');
            $data['excluded_entity_ids'] = $popup_excluded_entities ? explode(',', $popup_excluded_entities) : [];
            
            // Seçili olanları yükle
            $data['selected_entities'] = Entity::whereIn('id', $data['excluded_entity_ids'])
                ->select('id', 'entity_name', 'entity_code')
                ->get();

            return view("backend.settings.popup_settings", $data);

        } elseif($section == 'diger-ayarlar') {

            $general_price_list = Setting::where('name', 'general_price_list')->value('val');
            $order_with_contract = Setting::where('name', 'order_with_contract')->value('val');
            $warehouse_delivery_only = Setting::where('name', 'warehouse_delivery_only')->value('val');
            $password_change_frequency = Setting::where('name', 'password_change_frequency')->value('val');
            $min_payment_amount = Setting::where('name', 'min_payment_amount')->value('val');

            return view("backend.settings.other_settings", compact('general_price_list', 'warehouse_delivery_only', 'password_change_frequency', 'min_payment_amount', 'order_with_contract'));

            return redirect()->back()->with('error', 'Bölüm bulunamadı.');
        }


    }

    public function store(Request $request)
    {
        if($request->get('section') == 'varsayilan-fiyat-listesi') {
            $rules = [
                'default_price_list' => 'required|integer',
            ];
            $data = $this->validate($request, $rules);

            PriceList::query()
                ->update([
                    'is_default' => null
                ]);
            PriceList::where('id', $data['default_price_list'])->update(['is_default' => 1]);
            Flash::success("<i class='fas fa-check'></i> Varsayılan fiyat listesi güncellendi")->important();
            Setting::add('default_price_list', $data['default_price_list'], 'integer');
            return redirect()->back()->with('status', 'Ayarlar kaydedildi.');
        }

        if($request->get('section') == 'other_settings') {


            $default_pos = Setting::where('name', 'default_pos')->value('val');
            if($default_pos == $request->get('setting') and $request->get('setting_value') == 0) {
                return ['success' => false, 'message' => 'Bu POS varsayılan pos olduğu için pasif yapılamaz.'];
            }
            Setting::add($request->get('setting'), $request->get('setting_value'), 'pos');

            Log::info(label_case('Ayar Güncellendi') . ' | Kullanıcı:' . Auth::user()->name . '(ID:' . Auth::user()->id . ')');

            return ['success' => true, 'message' => 'Ayarlar kaydedildi.', 'setting' => $request->get('setting'), 'value' => $request->get('setting_value')];
        }

        if($request->get('section') == 'pos-settings') {

            if(empty($request->get('default_pos'))) {
                return redirect()->back()->with('error', 'Varsayılan POS seçiniz.');
            }

            Setting::add('default_pos', $request->get('default_pos'), 'string');
            Flash::success("<i class='fas fa-check'></i> Varsayılan POS güncellendi")->important();
            Log::info(label_case('Varsayılan POS Güncellendi') . ' | Kullanıcı:' . Auth::user()->name . '(ID:' . Auth::user()->id . ')');

            return redirect()->back()->with('status', 'Varsayılan POS bilgisi güncellendi.');
        }

        if($request->get('section') == 'popup-ayarlari') {
            $excluded_entities = $request->get('excluded_entities', []);
            $excluded_entities_string = implode(',', $excluded_entities);
            
            Setting::add('popup_excluded_entities', $excluded_entities_string, 'string');
            
            Log::info(label_case('Popup ayarları güncellendi') . ' | Kullanıcı:' . Auth::user()->name . '(ID:' . Auth::user()->id . ')');
            Flash::success("<i class='fas fa-check'></i> Popup ayarları güncellendi")->important();
            return redirect()->back()->with('status', 'Popup ayarları güncellendi.');
        }

        if($request->get('section') == 'update-installments') {
            $all = $request->all();
            foreach ($all as $key => $value) {
                Setting::add($key, $value, 'integer');
            }
            Log::info(label_case('Taksit bilgisi güncellendi') . ' | Kullanıcı:' . Auth::user()->name . '(ID:' . Auth::user()->id . ')');
            Flash::success("<i class='fas fa-check'></i> Taksit sayıları güncellendi")->important();
            return redirect()->back()->with('status', 'Taksit syısı güncellendi.');
        }

        if($request->get('section') == 'varsayilan-fiyat-listesi') {
            $rules = [
                'default_price_list' => 'required|integer',
            ];
            $data = $this->validate($request, $rules);

            PriceList::query()
                ->update([
                    'is_default' => null
                ]);
            PriceList::where('id', $data['default_price_list'])->update(['is_default' => 1]);
            Setting::add('default_price_list', $data['default_price_list'], 'integer');
            Log::info(label_case('Varsayılan Fiyat listesi güncellendi') . ' | Kullanıcı:' . Auth::user()->name . '(ID:' . Auth::user()->id . ')');
            Flash::success("<i class='fas fa-check'></i> Varsayılan fiyat listesi güncellendi")->important();
            return redirect()->back()->with('status', 'Ayarlar kaydedildi.');
        }

        $rules = Setting::getValidationRules();
        $data = $this->validate($request, $rules);

        $validSettings = array_keys($rules);

        foreach ($data as $key => $val) {
            if (in_array($key, $validSettings)) {
                Setting::add($key, $val, Setting::getDataType($key));
            }
        }

        return redirect()->back()->with('status', 'Ayarlar kaydedildi.');
    }

    public function searchEntities(Request $request)
    {
        $search = $request->get('search', '');
        $page = $request->get('page', 1);
        $perPage = 20;
        
        $entities = Entity::where(function($query) use ($search) {
                if ($search) {
                    $query->where('entity_name', 'LIKE', "%{$search}%")
                          ->orWhere('entity_code', 'LIKE', "%{$search}%");
                }
            })
            ->whereNotNull('is_default')
            ->select('id', 'entity_name', 'entity_code')
            ->orderBy('entity_name')
            ->paginate($perPage, ['*'], 'page', $page);
        
        $results = [];
        foreach ($entities->items() as $entity) {
            $results[] = [
                'id' => $entity->id,
                'text' => $entity->entity_name . ' (' . $entity->entity_code . ')'
            ];
        }
        
        return response()->json([
            'results' => $results,
            'pagination' => [
                'more' => $entities->hasMorePages()
            ]
        ]);
    }
}
