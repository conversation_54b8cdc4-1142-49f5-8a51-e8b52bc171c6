<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Cart;
use App\Models\Contract;
use App\Models\Dbs;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\PaymentTransaction;
use App\Models\Setting;
use Cassandra\Set;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Http\Request;
use Illuminate\Support\Number;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;
use App\Models\Entity;
use GuzzleHttp\Client;
use Laracasts\Flash\Flash;

class PaymentController extends Controller
{

    /**
     * Retrieves the view for the index page of the frontend.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        $user = auth()->user();
        $filtre = request()->filtre;


        $data['entities'] = [];
        $data['user'] = $user;

        if($user->hasRole('super admin'))
        {
            $payments = Payment::orderByDesc('id');
        }
        else
        {
            $payments = Payment::where('user_id', $user->id);
        }

        if($filtre == 'devam-edenler')
        {
            $payments = $payments->where('remaining_amount', '>', 0);
        } elseif($filtre == 'suresi-gecenler')
        {
            $payments = $payments->where('last_payment_date', '<', date('Y-m-d'))->where('remaining_amount', '>', 0);
        } elseif($filtre == 'tamamlananlar')
        {
            $payments = $payments->where('remaining_amount', 0);
        }

        $payments = $payments->paginate(20);
        $data['payments'] = $payments;
        return view('frontend.payment.index', $data);
    }

    public function show($id)
    {
        $user = auth()->user();
        $id = decode_id($id);
        $payment = Payment::findOrFail($id);
        $data['payment'] = $payment;

        $payment_form_active = true;
        if($payment->remaining_amount == 0)
        {
            $payment_form_active = false;
        }

        if($payment->paymentTransactions->count() > 0) {
            $last_transaction = PaymentTransaction::where('payment_id', $id)->whereNotNull('md_error_message')->orderByDesc('id')->first();
            if($last_transaction) $data['md_error_message'] = $last_transaction->md_error_message;
        }

        $data['payment_form_active'] = $payment_form_active;
        $data['user'] = $user;
        $data['allPosServices'] = config('laravel-pos.banks');

        return view('frontend.payment.credit_card', $data);

    }

    public function store(Request $request)
    {

        $input = $request->all();
        $user = auth()->user();

        if(empty($input['entity_id']) && empty($input['phone_number']))
        {
            return redirect()->back()->withErrors('Ödeme talebi için, bir cari seçiniz veya cep telefonu giriniz.')->withInput();
        }

        if (empty($input['entity_id'])) {
            $input['entity_id'] = $user->active_entity_id;
        }

        $input['user_id'] = $user->id;
        $input['remaining_amount'] = str_replace(',', '', $input['amount']);
        $input['last_payment_date'] = date('Y-m-d', strtotime($request->last_payment_date)) ?? null;

        $emails = [];
        if (isset($input['entity_id'])) {
            $entity = Entity::find($input['entity_id']);
            if ($entity) {
                $emails[] = $entity->email;
                if (empty($entity->email)) {
                    return redirect()->back()->withErrors('Seçili cari ('.ucwords($entity->entity_name).') için kayıtlı bir email adresi yok.')->withInput();
                }
            }
        }

        $min_payment_amount = Setting::where('name', 'min_payment_amount')->value('val');
        if ($input['amount'] < $min_payment_amount) {
            return redirect()->back()->withErrors(['error' => 'POS ile en az '. Number::currency($min_payment_amount, 'TRY', 'tr') . ' ödeme talep edilmelidir.'])->withInput();
        }

        if (empty($emails) and empty($input['phone_number'])) {
            return redirect()->back()->withErrors(['error' => 'Email listesi ve cep telefonu boş olamaz.'])->withInput();
        }

        if(empty($emails) && empty($input['phone_number']))
        {
            return redirect()->back()->withErrors('Ödeme talebinin gönderilebilmesi için seçili carinin kayıtlı email adresi veya cep telefonu gereklidir.')->withInput();
        }

        $payment = Payment::create($input);

        if(!empty($input['phone_number']))
        {
            $sms_message = number_format($input['amount']) . ' tutarında ödeme talebi oluşturuldu. Detaylar ve ödeme için: ' . route('payment.show', encode_id($payment->id));

            $client = new Client();
            $request_data = [
                'dil' => 'tr',
                'msgheader' => 'AKDAGYALITM',
                'usercode' => '8508404923',
                'password' => '92@BB6D',
                'gsmno' => $input['phone_number'],
                'message' => $sms_message
            ];
            $response = $client->get('https://api.netgsm.com.tr/sms/send/get/?'.http_build_query($request_data));
            Log::info(label_case('Yeni ödeme talebi: '. $sms_message) . ' | Kullanıcı:' . Auth::user()->name . '(ID:' . Auth::user()->id . ')');
            Flash::success("<i class='fas fa-check'></i> Ödeme talebi oluşturuldu")->important();

            return redirect()->back()->with('status', 'Yeni ödeme talebi oluşturuldu.');
        }

        $emails = []; // aktif edince kullanılacak
        if ($emails) {
            if ($payment->order_id) {
                $messageContent = $payment->entity->entity_name . ' cari firması için ' . date('d.m.y', strtotime($payment->last_payment_date)) . ' son ödeme tarihli, ' . $payment->order->doc_no . ' numaralı sipariş için ' . number_format($payment->amount, 2) . ' TL ödeme talebi oluşturuldu.';
            } else {
                $messageContent = $payment->entity->entity_name . ' cari firması için ' . date('d.m.y', strtotime($payment->last_payment_date)) . ' son ödeme tarihli, ' . number_format($payment->amount, 2) . ' TL ödeme talebi oluşturuldu.';
            }

            $messageContent .= '<br><br>Ödeme talebi detayları için <a href="' . route('payment.show', encode_id($payment->id)) . '">tıklayınız</a>';

            $data = array('name' => "Akdağ Portal", 'messageContent' => $messageContent);
            Mail::send(['html' => 'emails.mail'], $data, function ($message) use ($emails, $payment) {
                $message->to($emails)->subject('Ödeme Talebi: ' . encode_id($payment->id));
                $message->from('<EMAIL>', env('APP_NAME', 'B2B Portal'));
            });
        }

        return redirect()->route('odeme.index')->with('success', 'Ödeme tablosuna yeni kayıt eklendi.');
    }

    public function edit($id)
    {
        $payment = Payment::find($id);

        return view('frontend.payment.edit', compact('payment'));
    }

    public function update($id, Request $request)
    {
        $request->validate([
            'amount' => 'required',
            'last_payment_date' => 'required',
        ]);
        $payment = Payment::find($id);
        $input = $request->all();
        $input['remaining_amount'] = str_replace(',', '', $input['remaining_amount']);

        $payment->update($request->all());
        Flash::success("<i class='fas fa-check'></i> Ödeme talebi güncellendi")->important();

        return redirect()->route('odeme.index')->with('success', 'Ödeme talebi güncellendi.');
    }

    public function destroy()
    {
        $id = decode_id(request('id'));
        $payment = Payment::find($id);
        $payment->delete();

        return redirect()->route('odeme.index')->with('success', 'Ödeme talebi silindi.');
    }

    public function destroy_transaction()
    {
        $id = decode_id(request('id'));
        $payment = PaymentTransaction::find($id);
        $payment->delete();

        return redirect()->route('payment.transactions')->with('success', 'Ödeme işlemi silindi.');
    }
    public function show_transaction()
    {
        $id = decode_id(request('id'));
        $paymentTransaction = PaymentTransaction::find($id);

        $paymentTransaction_response = json_decode($paymentTransaction->response, true);

        $error_message = $paymentTransaction_response['error_message'] ?? null;

        return view('frontend.payment.modal.show_transaction', compact('paymentTransaction', 'error_message'));
    }
    public function payment_refunded()
    {
        $id = decode_id(request('id'));
        $paymentTransaction = PaymentTransaction::find($id);
        $paymentTransaction->update(['status' => 'refunded']);
        $payment = Payment::find($paymentTransaction->payment_id);
        $payment->update(['remaining_amount' => $payment->remaining_amount + $paymentTransaction->amount]);

        return ['success' => true, 'message' => 'Ödeme iade edildi olara işaretlendi'];

    }

    public function transactions()
    {
        $user = auth()->user();
        if (str_contains($user->email, 'akdagtasyunu.com')) {
            $entity_ids = Entity::whereNotNull('is_default')->pluck('id')->toArray();
        } else {
            $entity_ids = $user->entities->pluck('id')->toArray();
        }
        if (request()->ajax()) {
            $transaction = PaymentTransaction::query();


            if (!str_contains($user->email, 'akdagtasyunu.com') && !auth()->user()->hasRole('super admin')) {
                $transaction->whereIn('entity_id', $entity_ids);
            }

            if (request()->has('entity_id')) {
                $entity_id = request()->get('entity_id');
                if (!empty($entity_id)) {
                    $transaction->where('entity_id', $entity_id);
                }
            }

            if (request()->has('status') && !empty(request()->get('status'))) {
                $transaction->where('status', request()->get('status'));
            }

            if (!empty(request()->start_date) && !empty(request()->end_date)) {
                $start = date('Y-m-d', strtotime(request()->start_date));
                $end = date('Y-m-d', strtotime(request()->end_date));
                $transaction->whereDate('created_at', '>=', $start)
                    ->whereDate('created_at', '<=', $end);
            }

            return Datatables::of($transaction)
                ->addColumn(
                    'action', function ($row) {
                    $action =  '<ul class="nk-tb-actions gx-1">                 
                         <li>
                                                                        <div class="drodown">
                                                                            <a href="#" class="dropdown-toggle btn btn-icon btn-trigger" data-bs-toggle="dropdown"><em class="icon ni ni-more-h"></em></a>
                                                                            <div class="dropdown-menu dropdown-menu-end">
                                                                                <ul class="link-list-opt no-bdr">';
                    if ($row->status == 'approved') {
                        $action .=  '<li><a target="_blank" href="/odeme/pdf/' . encode_id($row->id) . '"><em class="icon ni ni-file-pdf"></em> Tahsilat Makbuzu</a></li>';
                    }
                    if (auth()->user()->hasRole('super admin')) {
                        $action .=  '<li><a data-id=" '.encode_id($row->id).' " class="delete-payment-transaction"><em class="icon ni ni-trash"></em> İşlemi Sil</a></li>';
                    }
                    if (auth()->user()->hasRole('super admin') && $row->status == 'approved') {
                        $action .=  '<li><a data-id=" '.encode_id($row->id).' " class="payment-refunded"><em class="icon ni ni-visa-alt"></em> Ödeme İade Edildi</a></li>';
                    }
                    $action .=                   '    </ul>
                                                                            </div>
                                                                        </div>
                                                                    </li></ul>';
                    return $action;
                } )
                ->editColumn('doc_no', function ($row) {
                    if(empty($row->description))
                        return $row->doc_no;
                    else
                        return $row->doc_no . ' <i class="text-info ni ni-info-fill" data-bs-toggle="tooltip" title="'.$row->description.'"></i>';
                })
                ->editColumn('created_at', function ($row) {
                    return $row->created_at->format('d.m.Y');
                })
             ->editColumn('created_at_hour', function ($row) {
                return $row->created_at->format('H:i');
            })
                ->editColumn('entity_name', function ($row) {
                    $entity = Entity::find($row->entity_id);
                    if ($entity) {
                        return limit_words($entity->entity_name, 3);
                    } else {
                        return '-';
                    }
                })
                ->editColumn('pos_name', function ($row) {
                    return mb_strtoupper($row->pos_name);
                })
                ->editColumn('amount', function ($row) {
                    return Number::currency($row->amount, 'TRY', 'tr');
                })
                ->editColumn('amount2', function ($row) {
                    return number_format($row->amount, 2, '.', '');
                })
                ->editColumn('card_holder', function ($row) {
                    return mb_strtoupper($row->card_holder);
                })
                ->editColumn('amount_with_installment', function ($row) {
                    return Number::currency($row->amount, 'TRY', 'tr');
                })
                ->editColumn('provision_number', function ($row) {
                    if($row->status == 'approved') {
                        return $row->provision_number;
                    } elseif($row->status == 'declined')  {
                        return '<a href="#" class="btn-modal" data-href="/odeme-islemi/' . encode_id($row['id']) . '" data-container="#modal_container"><span class="text-danger">Reddedildi</span></a>';
                    } elseif($row->status == 'refunded')  {
                        return '<span class="text-danger">İade Edildi</span>';
                    } else {
                        return '-';
                    }
                })
                ->editColumn('installment_count', function ($row) {
                    return $row->installment_count;
                })
                ->editColumn('status_color', function ($row) {
                    if ($row->status == 'declined') {
                        return '#ffefef'; // soft red
                    }
                })
                ->setRowAttr([
                    'data-href' => function ($row) {
                        return  action([\App\Http\Controllers\Frontend\InvoiceController::class, 'show'], [$row->id]);
                    }, ])
                ->rawColumns(['action', 'doc_date', 'doc_no' ,'co_desc', 'provision_number'])
                ->removeColumn('co_code')
                ->make(true);
        }


        $data['entities'] = Entity::whereIn('id' ,$entity_ids)->orderBy('entity_name')->pluck('entity_name as name', 'id');

        return view('frontend.payment.transactions', $data);
    }

    public function view_pdf($id)
    {
        $user = auth()->user();

        $id = decode_id($id);

            $transaction = PaymentTransaction::where('id', $id)->first();


        if (!$transaction) {
            return 'Ödeme işlemi bulunamadı.';
        }

        $response = json_decode($transaction->response, true);

        if ($transaction->entity_id) {
            $cari_unvani = limit_words($transaction->entity->entity_name,3) ?? '';
        } else {    // if entity_id is null
            $cari_unvani = '';
        }

        $data['cari_unvani'] = $cari_unvani;
        $data['cari_kodu'] = $transaction->entity->entity_code ?? '';
        $data['ad_soyad'] = mb_strtoupper($transaction->card_holder);
        $data['tarih'] = date('d.m.Y', strtotime($transaction->created_at));
        $data['kart_no'] = $transaction->card_number;
        $data['tutar'] = number_format($transaction->amount, 2, ',', '.');
        $data['genel_toplam'] = $data['tutar'];
        $data['provizyon_no'] = $transaction->provision_number;
        $data['banka_adi'] = mb_strtoupper($transaction->pos_name);
        $data['taksit_sayisi'] = $transaction->installment_count;

        $public_path = public_path('storage/pdf/payments/' . $transaction->id . '.pdf');

        return view('frontend.pdfs.payment', $data);
//        Pdf::view('frontend.pdfs.payment', $data)
//            ->format('a4')
//            ->save($public_path);
    }

    public function pay_with_dbs()
    {
        $user = auth()->user();
        $amount = request()->amt;
        $id = request()->id;
        $dbs = Dbs::find($id);

        $contract_id = Cart::where('user_id', $user->id)->where('entity_id', $user->active_entity_id)->whereNull('ordered_at')->value('contract_id');
        $contract = Contract::find($contract_id);
        $amt = Cart::where('contract_id', $contract->id)->where('entity_id', $user->active_entity_id)->where('user_id', $user->id)->whereNull('ordered_at')->sum('amt');
        $amt_vat = Cart::where('contract_id', $contract->id)->where('entity_id', $user->active_entity_id)->where('user_id', $user->id)->whereNull('ordered_at')->sum('amt_vat');
        $amt_total = $amt + $amt_vat;

        if ($amount > $amt_total) {
            return ['success' => false, 'message' => 'Ödeme tutarı sepet tutarından fazla olamaz!'];
        }
        if ($amount == $amt_total) {
            return ['success' => true, 'message' => 'Sepet tutarının tamamı ' . $dbs->doc_no . ' ile ödendi!'];
        }

        if ($amount < $amt_total) {
            return ['success' => true, 'message' => 'Ödenen tutar: ' . Number::currency($amount, 'TRY', 'tr') . ' <br> Ödeme yapılan hesap no: ' . $dbs->doc_no . ' <br>Sipariş için kalan tutar: ' . ($amt_total - $amount) . ' TL.'];
        }

        return ['success' => true, 'message' => 'Girilen tutar : ' . $amount . ' TL. Ödeme işlemi başarılı.'];
    }

}
