<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Company;
use App\Models\DailyCurRate;
use App\Models\Price;
use App\Models\Product;
use App\Models\Tax;
use Illuminate\Http\Request;
use App\Models\Contract;
use Illuminate\Support\Number;
use Illuminate\Support\Facades\DB;

class CartController extends Controller
{
    /**
     * Retrieves the view for the index page of the frontend.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        $action = request()->action;
        $user = auth()->user();

    }


    public function store(Request $request)
    {

        $user = auth()->user();
        $company = Company::where('is_default', 1)->first();
        $product_id = $request->product_id;
        $zz_ton_price = $request->ton_price ?? null;
        if(!is_numeric($product_id)) return ['success' => false, 'message' => 'Lütfen ürün seçiniz!'];
        $qty = $request->qty;
        $quantity_pallet = $request->quantity_pallet;
        $contract_id = $request->contract_id;
        if (empty($contract_id)) {
            $contract_id = 0;
            $sales_person_id = DB::table('company_entity')->where('co_id', $company->id)->where('entity_id', $user->active_entity_id)->value('sales_person_id');
        } else {
            $contract = Contract::find($contract_id);
            $sales_person_id = 0;
            if ($contract) {
                $sales_person_id = $contract->sales_person_id;
                $zz_ton_price = $contract->zz_ton_price;
            }
        }
        $vat_rate = 0;
        if ($request->vat_id) {
            $vat_rate =  Tax::where('id', $request->vat_id)->value('tax_rate');
        }
        $cart_item_input['price_list_id'] = $request->price_list_id;
        $cart_item_input['vat_id'] = $request->vat_id;
        $cart_item_input['vat_rate'] = $vat_rate;
        $cart_item_input['zz_ton_price'] = $zz_ton_price;
        // Değerleri parse et - gelen değer zaten sayı ise direkt kullan, değilse Türk formatından çevir
        $unit_price_tra = is_numeric($request->unit_price_tra) ? floatval($request->unit_price_tra) : $this->parseTurkishCurrency($request->unit_price_tra);
        $amt_tra = is_numeric($request->amt) ? floatval($request->amt) : $this->parseTurkishCurrency($request->amt);
        $amt_vat_tra = is_numeric($request->amt_vat) ? floatval($request->amt_vat) : $this->parseTurkishCurrency($request->amt_vat);
        
        // Debug: Gelen değerleri kontrol et
        \Log::info('Cart Debug - Raw values', [
            'amt_raw' => $request->amt,
            'amt_vat_raw' => $request->amt_vat,
            'amt_cleaned' => $amt_tra,
            'amt_vat_cleaned' => $amt_vat_tra,
            'unit_price_tra' => $unit_price_tra
        ]);
        $product = Product::find($product_id);

        $cart = Cart::where('user_id', $user->id)->where('entity_id', $user->active_entity_id)->where('contract_id', $contract_id)->whereNull('ordered_at')->first();


        $cur_rate_tra = 1; // Varsayılan
        $cur_rate_type_id = 0; // 234; // TCMB-SATIS
        $cur_tra_id = $request->input('cur_tra_id', 114); // TL
        if ($cur_tra_id !== 114) {
            $cur_rate_tra = DailyCurRate::where('cur_to_id', 114)->where('cur_from_id', $cur_tra_id)->where('cur_rate_type_id', $cur_rate_type_id)->orderByDesc('id')->value('cur_rate_tra');
        }
        $cart_array = [
            'user_id' => $user->id,
            'entity_id' => $user->active_entity_id,
            'contract_id' => $contract_id,
            'sales_person_id' => $sales_person_id,
            'cur_tra_id' => 114, // todo varsayılan para birimi id si düzenlenecek
            'co_id' => 2725, // todo varsayılan firma id si düzenlenecek
            'branch_id' => 6774, // todo varsayılan şube id si düzenlenecek
            'amt' => 0,
            'amt_vat' => 0,
            'item_count' => 0,
            'zz_tone_price' => $zz_ton_price,
        ];

        if($cart == null){
            $cart = Cart::create($cart_array);
        }

        // todo fiyatlar düzenlenecek uyumapi ile çekilecek. sipariş uyuma aktarılırken tekrar kontrol edilsin mi?

        $cart_item_input_match = [
            'cart_id' => $cart->id,
            'product_id' => $product_id,
        ];
        $cart_item_input['qty'] = $qty;
        $cart_item_input['entity_id'] = $user->active_entity_id;
        $cart_item_input['quantity_pallet'] = $quantity_pallet;
        $cart_item_input['cur_rate_tra'] = $cur_rate_tra;
        $cart_item_input['cur_tra_id'] = $cur_tra_id;
        $cart_item_input['cur_rate_type_id'] = $cur_rate_type_id;
        $cart_item_input['unit_id'] = $product->unit_id;
        $cart_item_input['amt_tra'] = $amt_tra;
        $cart_item_input['amt'] = $amt_tra * $cur_rate_tra;
        $cart_item_input['amt_vat_tra'] = $amt_vat_tra;
        $cart_item_input['amt_vat'] = $amt_vat_tra * $cur_rate_tra;
        
        // Debug: Hesaplanan değerleri kontrol et
        \Log::info('Cart Debug - Calculated values', [
            'amt_tra' => $cart_item_input['amt_tra'],
            'amt' => $cart_item_input['amt'],
            'amt_vat_tra' => $cart_item_input['amt_vat_tra'],
            'amt_vat' => $cart_item_input['amt_vat'],
            'cur_rate_tra' => $cur_rate_tra
        ]);
        $cart_item_input['amt_receipt_tra'] = $amt_tra+$amt_vat_tra;
        $cart_item_input['amt_receipt'] = $cart_item_input['amt']+$cart_item_input['amt_vat'];
        $cart_item_input['unit_price'] = $unit_price_tra;
        $cart_item_input['unit_price_tra'] = $unit_price_tra;
        $cart_item_input['whouse_id'] = 3764; // todo varsayılan depo id si düzenlenecek

        // Sözleşme seçilmişse sepet tutarının sözleşme kalan tutarını aşıp aşmadığını kontrol et
        if ($contract_id > 0) {
            $contract = Contract::find($contract_id);
            if ($contract) {
                // Mevcut sepetteki tüm ürünlerin toplam tutarını hesapla
                $currentCartTotal = $cart->cart_items()->sum('amt') + $cart->cart_items()->sum('amt_vat');
                $newItemTotal = $cart_item_input['amt'] + $cart_item_input['amt_vat'];
                $totalCartAmount = $currentCartTotal + $newItemTotal;

                if ($totalCartAmount > $contract->remaining_amount) {
                    $maxAddableAmount = $contract->remaining_amount - $currentCartTotal;
                    return [
                        'success' => false,
                        'message' => 'Sepet tutarı sınırı aşıldı! Mevcut sepet tutarı: ' . number_format($currentCartTotal, 2) . ' ₺. Sözleşme kalan tutarı: ' . number_format($contract->remaining_amount, 2) . ' ₺. Eklenebilecek maksimum tutar: ' . number_format($maxAddableAmount, 2) . ' ₺'
                    ];
                }
            }
        }

        try {
            CartItem::updateOrCreate($cart_item_input_match,$cart_item_input);
            $cart->update([
                'amt' => $cart->cart_items->sum('amt'),
                'amt_tra' => $cart->cart_items->sum('amt_tra'),
                'amt_vat' => $cart->cart_items->sum('amt_vat'),
                'amt_vat_tra' => $cart->cart_items->sum('amt_vat_tra'),
                'amt_receipt_tra' => $cart->cart_items->sum('amt_tra') + $cart->cart_items->sum('amt_vat_tra'),
                'amt_receipt' => $cart->cart_items->sum('amt') + $cart->cart_items->sum('amt_vat'),
                'cur_rate_type_id' => $cur_rate_type_id,
                'cur_rate_tra' => $cur_rate_tra,
                'item_count' => $cart->cart_items->count(),
                'quantity_pallet' => $cart->cart_items->sum('quantity_pallet'),
                'qty' => $cart->cart_items->sum('qty'),
                'zz_ton_price' => $zz_ton_price,
            ]);

            if($cart->item_count>0) { $cart_count_msg = $cart->item_count . ' ürün sepette'; } else { $cart_count_msg = 'Sepet işlemleri'; }
            $cart_total_amount = (($cart->amt+$cart->amt_vat)>0) ? Number::currency($cart->amt+$cart->amt_vat, 'TRY', 'tr') : 'Ödeme işlemleri';
            $this->addCoveringFeeToCart($product, $qty, $cart);

            return ['success' => true, 'message' => $product->item_name . ' sepete eklendi! Sepete gitmek için için "Sonraki Adım" butonuna tıklayınız.', 'cart_total_amount' => $cart_total_amount, 'cart_count_msg' => $cart_count_msg];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }


    }

    private function addCoveringFeeToCart($product, $qty, $cart)
    {

        if (in_array($product->categories4_id, [2181, 2182, 2183])) { //kaplama türü alüminyum folyo veya siyah cam tülü
            if ($product->categories4_id == 2181) {
                $item_id = 124901;
            } else if ($product->categories4_id == 2182) {
                $item_id = 122739;
            } else if ($product->categories4_id == 2183) {
                $item_id = 124900;
            }
            $covering_price = Price::select('unit_price_tra', 'cur_tra_id', 'unit_id')->where('item_id', $item_id)->where('price_list_m_id', 951)->first();

            if ($covering_price) {
                $cur_rate = DailyCurRate::where('cur_from_id', $covering_price->cur_tra_id)->where('cur_rate_type_id', 277)->orderByDesc('id')->value('cur_rate_tra');
                $covering_unit_price = $covering_price->unit_price_tra * $cur_rate;
                $covering_fee = $qty * $covering_unit_price;
                $user = auth()->user();
                $cart_item_input_match = [
                    'cart_id' => $cart->id,
                    'product_id' => $item_id,
                ];
                $cart_item_input['price_list_id'] = 951;
                $cart_item_input['vat_id'] = 449;
                $cart_item_input['vat_rate'] = Tax::find(449)->tax_rate;
                $cart_item_input['qty'] = $qty;
                $cart_item_input['entity_id'] = $user->active_entity_id;
                $cart_item_input['cur_rate_tra'] = 1.0000;
                $cart_item_input['cur_tra_id'] = 114;
                $cart_item_input['parent_id'] = $product->id;
                $cart_item_input['unit_id'] = $covering_price->unit_id;
                $cart_item_input['amt_tra'] = $covering_fee;
                $cart_item_input['amt'] = $covering_fee;
                $cart_item_input['amt_vat_tra'] = $covering_fee * 0.20;
                $cart_item_input['amt_vat'] = $covering_fee * 0.20;
                $cart_item_input['unit_price'] = $covering_unit_price;
                $cart_item_input['unit_price_tra'] = $covering_unit_price;
                $cart_item_input['whouse_id'] = 3764; // todo varsayılan depo id si düzenlenecek
                CartItem::updateOrCreate($cart_item_input_match,$cart_item_input);
            }
        }
        // return ['covering_fee' => $covering_price, 'covering_unit_price' => $covering_unit_price];
    }

    /**
     * Türk para formatını parse et
     * Örnek: "722.608,13 ₺" -> 722608.13
     */
    private function parseTurkishCurrency($value)
    {
        if (empty($value)) {
            return 0;
        }
        
        // ₺ sembolü ve boşlukları temizle
        $value = str_replace(['₺', ' '], '', $value);
        
        // Binlik ayraç olan noktaları kaldır
        $value = str_replace('.', '', $value);
        
        // Ondalık ayraç olan virgülü noktaya çevir
        $value = str_replace(',', '.', $value);
        
        return floatval($value);
    }


    public function edit()
    {
        $action = request()->action;
        if ($action == 'approve') {
            $cart = Cart::find(11);

            return view('frontend.cart.approve');
        }
    }

    public function approve()
    {
        $id = request()->id;
        $user = Auth()->user();


        try {
            $create_user = uyumapi('SELECT us_id FROM users WHERE us_username = \'' . $user->username . '\'');
            $create_user_id = $create_user[0]['us_id'];
        } catch (\Exception $e) {
            \Log::emergency("Dosya:" . $e->getFile() . "Satır:" . $e->getLine() . "Mesaj:" . $e->getMessage());
        }

        $output = ['success' => 1,
            'message' => 'Sipariş Uyumsoft\'a aktarıldı.'
        ];


        return response()->json($output);

    }

    public function getCartItemCount(Request $request)
    {
        try {
            $user = auth()->user();
            $contractId = $request->contract_id;
            
            if (!$contractId) {
                return response()->json(['count' => 0]);
            }
            
            $cart = Cart::with('cart_items')->where('user_id', $user->id)
                ->where('entity_id', $user->active_entity_id)
                ->where('contract_id', $contractId)
                ->whereNull('ordered_at')
                ->first();
                
            $count = 0;
            if ($cart && $cart->cart_items) {
                $count = $cart->cart_items->count();
            }
            
            return response()->json(['count' => $count]);
        } catch (\Exception $e) {
            return response()->json(['count' => 0, 'error' => $e->getMessage()]);
        }
    }


}
