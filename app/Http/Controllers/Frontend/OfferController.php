<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;
use App\Models\Entity;
use App\Models\Order;
use App\Models\Offer;
use Illuminate\Support\Number;


use Illuminate\Http\Request;

class OfferController extends Controller
{

    /**
     * Retrieves the view for the index page of the frontend.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        $user = auth()->user();
        if (str_contains($user->email, 'akdagtasyunu.com')) {
            $entity_ids = Entity::whereNotNull('is_default')->pluck('id')->toArray();
        } else {
            $entity_ids = $user->entities->pluck('id')->toArray();
        }
            if (request()->ajax()) {

                $waybill = Offer::select('e.entity_name','offers.id', 'offers.doc_no','offers.id as offer_m_id', 'offers.amt', 'offers.amt_vat', 'offers.doc_date', 'co.co_code','co.co_desc', 'cur_code', 'e.entity_name')
                    ->leftJoin('entities as e', 'offers.entity_id', '=', 'e.id')
                    ->leftJoin('currencies as cu', 'offers.cur_tra_id', '=', 'cu.id')
                    ->leftJoin('companies as co', 'offers.co_id', '=', 'co.id')
                    ->whereIn('entity_id', $entity_ids);

                if (request()->has('entity_id')) {
                    $entity_id = request()->get('entity_id');
                    if (!empty($entity_id)) {
                        $waybill->where('offers.entity_id', $entity_id);
                    }
                }
                if (request()->has('status')) {
                    $status = request()->get('status');
                    $end = date('Y-m-d', time());
                    if ($status == 'active') {
                        $waybill->whereDate('doc_date', '>=', $end);
                    } else if ($status == 'passive') {
                        $waybill->whereDate('doc_date', '<', $end);
                    }
                }
                if (!empty(request()->start_date) && !empty(request()->end_date)) {
                    $start = date('Y-m-d', strtotime(request()->start_date));
                    $end = date('Y-m-d', strtotime(request()->end_date));
                    $waybill->whereDate('doc_date', '>=', $start)
                        ->whereDate('doc_date', '<=', $end);
                }
                return Datatables::of($waybill)
                    ->addColumn(
                        'action', function ($row) {
                        $action = '<ul class="nk-tb-actions gx-1">
                          
                                                                    <li>
                                                                        <div class="drodown">
                                                                            <a href="#" class="dropdown-toggle btn btn-icon btn-trigger" data-bs-toggle="dropdown"><em class="icon ni ni-more-h"></em></a>
                                                                            <div class="dropdown-menu dropdown-menu-end">
                                                                                <ul class="link-list-opt no-bdr">
                                                                                    <li><a data-href="teklif/' . $row->id . '/detay" data-container="#modal_container" class="btn-modal"><em class="icon ni ni-eye"></em><span>Detay</span></a></li>
                                                                                </ul>
                                                                            </div>
                                                                        </div>
                                                                    </li>
                                                                </ul>';
                        return $action;
                    } )
                    ->editColumn('doc_no', function ($row) {
                        if(empty($row->description))
                            return $row->doc_no;
                        else
                            return $row->doc_no . ' <i class="text-info ni ni-info-fill" data-bs-toggle="tooltip" title="'.$row->description.'"></i>';
                    })
                    ->editColumn('doc_date', function ($row) {
                        return date('d.m.Y',strtotime($row->doc_date));
                    })
                    ->editColumn('entity_name', function ($row) {
                        return '<span data-bs-toggle="tooltip" data-bs-placement="top" title="'.$row->entity_name.'">' . limit_words($row->entity_name,3) . '</span>';
                    })
                    ->editColumn('co_desc', function ($row) {

                        return '<div class="user-card" data-bs-toggle="tooltip" data-bs-title="'.$row->co_desc.'">
                                                                <div class="user-avatar bg-dim-warning d-none d-sm-flex">
                                                                    <span>'.$row->co_code.'</span>
                                                                </div>                   
                                                            </div>';
                    })

                    ->editColumn('amt', function ($row) {
                        return Number::currency($row->amt, $row->cur_code, 'tr');
                    })
                    ->editColumn('amt_vat', function ($row) {
                        return number_format($row->amt_vat ,2) . ' '. $row->cur_code;
                    })

                    ->setRowAttr([
                        'data-href' => function ($row) {
                            return  action([\App\Http\Controllers\Frontend\OfferController::class, 'show'], [$row->id]);
                        }, ])
                    ->rawColumns(['action', 'co_desc', 'doc_no', 'entity_name'])
                    ->make(true);
            }

        $data['entities'] = Entity::whereIn('id' ,$entity_ids)->orderBy('entity_name')->pluck('entity_name as name', 'id');

        return view('frontend.offer.index', $data);
    }

    public function show($id)
    {
        $order = Order::find($id);
        $orderDetails = uyumapi("SELECT amt, amt_vat, qty, shipping_date, unit_price, line_no, item_name 
       FROM psmt_order_d
       LEFT JOIN invd_item ON psmt_order_d.item_id = invd_item.item_id
       where order_m_id = " .$order->id ." ORDER BY line_no ASC"
        );

        $data['order'] = $order;
        $data['orderDetails'] = $orderDetails;

        return view('frontend.order.modal.show', $data);

    }

    public function detail($id)
    {
        $offer = Offer::find($id);
        
        try {
            $offerDetails = uyumapi(
                "SELECT amt, amt_vat, qty, unit_price, line_no, item_name, item_code
           FROM psmt_offer_d
           LEFT JOIN invd_item ON psmt_offer_d.item_id = invd_item.item_id
           where offer_m_id = " .$offer->id ." ORDER BY line_no ASC"
            );

            // Ensure offerDetails is array
            if (!is_array($offerDetails)) {
                $offerDetails = [];
            }
        } catch (\Exception $e) {
            $offerDetails = [];
        }

        $data['offer'] = $offer;
        $data['offerDetails'] = $offerDetails;

        return view('frontend.offer.modal.detail', $data);
    }


    public function stockCards()
    {
        $user_id = auth()->user();
        if (!$user_id) {
            return redirect()->route('login');
        }
        $user_id = $user_id->id;

        $items = uyumapi('SELECT item_id, item_name, item_code from INVD_ITEM  order by item_id desc limit 1000');

        return view('frontend.order.stock_cards')->with(compact('items'));
    }





}
