<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\BinNumber;
use App\Models\Cart;
use App\Models\Entity;
use App\Models\PaymentTransaction;
use Illuminate\Container\Container;
use Illuminate\Http\Request;
use Mews\Pos\Entity\Card\CreditCardInterface;
use Mews\Pos\Exceptions\CardTypeNotSupportedException;
use Mews\Pos\Exceptions\CardTypeRequiredException;
use Mews\Pos\Exceptions\HashMismatchException;
use Mews\Pos\Factory\CreditCardFactory;
use Mews\Pos\Gateways\PayFlexV4Pos;
use Mews\Pos\PosInterface;
use App\Models\Payment;
use App\Models\Setting;
use App\Mail\NewPaymentMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Number;
use GuzzleHttp\Client;

class PosPaymentController extends Controller
{
    private string $paymentModel = PosInterface::MODEL_3D_SECURE;

    public function __construct(
        private PosInterface $pos,
        private Container    $container,

    )
    {
    }

    public function make_payment(Container $container)
    {

        $payment_id = request()->get('id');
        if ($payment_id) {
            $payment = Payment::find($payment_id);
            $data['payment'] = $payment;
            $amount = $payment->amount;

            if ($payment) {
                if ($payment->paymentTransactions->count() > 0) {
                    $last_transaction = PaymentTransaction::where('payment_id', $payment_id)->whereNotNull('md_error_message')->orderByDesc('id')->first();
                    if ($last_transaction) $data['md_error_message'] = $last_transaction->md_error_message;
                }
            }
        }

        if (empty($amount)) {
            $amount = request()->get('amount');
        }

        $user = auth()->user();

        $data['user'] = $user;

        $data['payment_form_active'] = true;
        $data['new_payment'] = true;
        $data['amount'] = $amount;

        return view('frontend.payment.credit_card', $data);
    }

    private function posService(string $bank): PosInterface
    {
        return $this->container->get('laravel-pos:gateway:' . $bank);
    }

    private function getPosService(string $bank): PosInterface
    {
        return $this->container->get('laravel-pos:gateway:' . $bank);
    }

    /**
     * route: /payment/3d/form
     * Kullanicidan kredi kart bilgileri alip buraya POST ediyoruz
     */
    public function form(Request $request)
    {
        $user = auth()->user();
        $session = $request->getSession();
        $default_pos = Setting::where('name', 'default_pos')->value('val');
        $selectedPos = request()->get('bank');
        $installment = $request->get('installment') ?? 1;

        // tek çekim seçtiyse varsayılan postan odmee alinacak
        if($installment == 1) {
            $selectedPos = $default_pos;
        }
        
        // taksit seçtiyse
        if($installment > 1) {
            if (!empty($request->get('number'))) {
                $bin_number = substr(str_replace(' ', '', $request->get('number')), 0, 6);
                if ($bin_number) {
                    $selectedPos = BinNumber::where('bin', $bin_number)->value('pos_name');
                    $selectedPos_active = Setting::where('name', $selectedPos)->value('val');
                    // kartın posu aktif değilse varsayılan posa yönlendir
                    if ($selectedPos_active == 0) {
                        $selectedPos = null;
                    }
                }
            }
        }

        $payment_id = request()->get('payment_id', null);
        $cart_id = request()->get('cart_id', null);
        $amount = request()->get('amount');
        $amount = str_replace('.', '', $amount);
        $amount = str_replace(',', '.', $amount);

        if (empty($selectedPos)) {
            $selectedPos = $default_pos;
        }

        $payment = Payment::find($payment_id);

        // serbest ödeme için payment_id zorunlu
        if (!$payment) {
            $payment = Payment::create([
                'user_id' => $user->id,
                'entity_id' => $user->active_entity_id,
                'amount' => $amount,
                'card_holder' => request()->get('name'),
                'card_number' => ccMasking(request()->get('number')),
                'remaining_amount' => $amount,
            ]);
            $payment_id = $payment->id;
        } else {
            $payment->update([
                'card_holder' => request()->get('name'),
                'card_number' => ccMasking(request()->get('number')),
            ]);
        }
        $txnCode = '';
        if ($selectedPos == 'akbank') {
            $txnCode = '3000';
        }

        $this->pos = $this->posService($selectedPos);
        $session->set('selected_pos', $selectedPos);

        $transaction = $request->get('tx', PosInterface::TX_TYPE_PAY_AUTH);
        $amount = number_format((float)$amount, 2, '.', '');
        $callbackUrl = url("/payment/3d/response");
        $order = $this->createNewOrder(
            $this->paymentModel,
            $callbackUrl,
            $request->getClientIp(),
            $request->get('currency', PosInterface::CURRENCY_TRY),
            $txnCode,
            $request->get('installment'),
            $payment_id,
            $cart_id,
            $amount,
        );
        $session->set('order', $order);

        $card = $this->createCard($this->pos, $request->request->all());

        /**
         * PayFlex'te provizyonu (odemeyi) tamamlamak icin tekrar kredi kart bilgileri isteniyor,
         * bu yuzden kart bilgileri kaydediyoruz
         */
        if ($this->pos::class === PayFlexV4Pos::class) {
            $session->set('card', $request->request->all());
        }
        $session->set('tx', $transaction);

        try {
            $formData = $this->pos->get3DFormData($order, $this->paymentModel, $transaction, $card);

        } catch (\Throwable $e) {
            Log::error($e);
            return redirect()->route('odeme.index')->withErrors(['error' => 'Payment processing error: ' . $e->getMessage()]);
        }

        return view('redirect-form', [
            'formData' => $formData,
        ]);
    }

    /**
     * route: /payment/3d/response
     * Kullanici bankadan geri buraya redirect edilir.
     * Bu route icin CSRF disable edilmesi gerekiyor.
     */
    public function response(Request $request)
    {
        $session = $request->getSession();
        $this->pos = $this->posService($session->get('selected_pos'));
        $transaction = $session->get('tx', PosInterface::TX_TYPE_PAY_AUTH);

        // Log basic bank response info
        Log::info('Bank response received', [
            'method' => $request->getMethod(),
            'pos_class' => get_class($this->pos)
        ]);
        
        // KuveytPos için mewebstudio/pos paketini kullanmayı dene
        if ($session->get('selected_pos') === 'kuveytpos') {
            try {
                // mewebstudio/pos paketi ile ödeme işlemini dene
                $order = $session->get('order');
                $card = null; // KuveytPos 3D Secure'da kart bilgisi gerekmez (provision aşamasında)
                $this->pos->payment($this->paymentModel, $order, $transaction, $card);
                $response = $this->pos->getResponse();
                
                if ($this->pos->isSuccess()) {
                    Log::info('KuveytPos payment successful with mewebstudio/pos package');
                    $session->set('last_response', $response);
                    goto handle_payment_result;
                }
                
            } catch (\Exception|\Error $e) {
                Log::warning('KuveytPos mewebstudio/pos failed, falling back to manual processing');
            }

            // Paket başarısız olursa manuel işleme geç
            $kuveytResponse = $this->processKuveytPosManually($request, $session);
            if ($kuveytResponse) {
                $response = $kuveytResponse;
                $session->set('last_response', $response);
                $order = $session->get('order');
                goto handle_payment_result;
            } else {
                Log::error('KuveytPos manual processing failed');
                $response = [
                    'status' => 'error',
                    'error_message' => 'Payment processing failed',
                    'md_error_message' => 'Ödeme işlemi başarısız oldu. Lütfen tekrar deneyiniz.',
                    'installment_count' => 0,
                    'ref_ret_num' => null,
                ];
                $session->set('last_response', $response);
                $order = $session->get('order');
                goto handle_payment_result;
            }
        }

        // bankadan POST veya GET ile veri gelmesi gerekiyor
        if (($request->getMethod() !== 'POST')
            // PayFlex-CP GET request ile cevapliyor
            && ($request->getMethod() === 'GET' && ($this->pos::class !== \Mews\Pos\Gateways\PayFlexCPV4Pos::class || [] === $request->query->all()))
        ) {
            Log::warning('Invalid request method or empty data from bank');
            return redirect('/');
        }

        $card = null;
        $card_number = null;
        $card_holder = null;
        $entity_id = null;
        $phone_number = null;
        
        $order = $session->get('order');
        if (!$order) {
            throw new \Exception('Sipariş bulunamadı, session sıfırlanmış olabilir.');
        }

        $response = [
            'status' => 'error',
            'error_message' => '',
            'md_error_message' => '',
            'installment_count' => 0,
            'ref_ret_num' => null,
        ];

        try {
            if ($this->pos::class === \Mews\Pos\Gateways\PayFlexV4Pos::class) {
                $savedCard = $session->get('card');
                $card = $this->createCard($this->pos, $savedCard);
            }

            if($session->get('selected_pos') == 'akbank'){
                // İkinci istek - Asıl ödeme işlemi
                $order = array_merge($order, [
                    'txnCode' => '1000'
                ]);
            }

            // Log basic payment info
            Log::info('Payment request', [
                'order_id' => $order['id'],
                'amount' => $order['amount'],
                'selected_pos' => $session->get('selected_pos')
            ]);

            // KuveytPos için özel kontrol
            if ($session->get('selected_pos') === 'kuveytpos') {
                // KuveytPos'tan gelen yanıtı kontrol et
                if (!$request->has('AuthenticationResponse') &&
                    !$request->has('MD') &&
                    !$request->has('MerchantOrderId')) {
                    Log::warning('KuveytPos: Required parameters missing from bank response');

                    $response = [
                        'status' => 'error',
                        'error_message' => 'Invalid response from bank',
                        'md_error_message' => 'Bankadan geçersiz yanıt alındı. Lütfen tekrar deneyiniz.',
                        'installment_count' => 0,
                        'ref_ret_num' => null,
                    ];
                    goto handle_payment_result;
                }
            }

            try {
                // KuveytPos için özel işlem
                if ($session->get('selected_pos') === 'kuveytpos') {
                    // KuveytPos için özel try-catch
                    try {
                        $this->pos->payment($this->paymentModel, $order, $transaction, $card);
                        $response = $this->pos->getResponse();

                        if ($this->pos->isSuccess()) {
                            Log::info('KuveytPos payment completed successfully');
                        }

                    } catch (\Error $kuveytError) {
                        Log::error('KuveytPos specific error', [
                            'error_message' => $kuveytError->getMessage()
                        ]);

                        // KuveytPos XML parsing hatası - manuel olarak işle
                        if (strpos($kuveytError->getMessage(), 'attributes') !== false ||
                            strpos($kuveytError->getMessage(), 'Attempt to read property') !== false) {

                            // Manuel olarak response oluştur
                            $response = $this->handleKuveytPosManualResponse($request, $order);
                            if (!$response) {
                                throw $kuveytError; // Manuel işlem de başarısız olursa orijinal hatayı fırlat
                            }
                        } else {
                            throw $kuveytError;
                        }
                    }
                } else {
                    // Diğer POS'lar için normal işlem
                    $this->pos->payment($this->paymentModel, $order, $transaction, $card);
                    $response = $this->pos->getResponse();
                }

                Log::info('Payment response received', [
                    'pos_type' => $session->get('selected_pos'),
                    'is_success' => method_exists($this->pos, 'isSuccess') ? $this->pos->isSuccess() : false
                ]);

            } catch (\Error $e) {
                Log::error('Payment processing failed with fatal error', [
                    'error_message' => $e->getMessage()
                ]);

                // KuveytPos serializer hatası için özel mesaj
                if (strpos($e->getMessage(), 'attributes') !== false ||
                    strpos($e->getMessage(), 'Attempt to read property') !== false) {
                    Log::error('KuveytPos XML parsing failed - likely payment declined or invalid response');

                    // Ödeme başarısız olduğu için error response oluştur
                    $response = [
                        'status' => 'declined',
                        'error_message' => 'Payment declined by bank',
                        'md_error_message' => 'Ödeme bankaca reddedildi veya geçersiz yanıt alındı. Kart bilgilerinizi kontrol ederek tekrar deneyiniz veya farklı bir kart kullanınız.',
                        'installment_count' => 0,
                        'ref_ret_num' => null,
                    ];
                } else {
                    // Diğer hatalar için genel error response
                    $response = [
                        'status' => 'error',
                        'error_message' => 'Payment processing error',
                        'md_error_message' => 'Ödeme işlemi sırasında bir hata oluştu: ' . $e->getMessage(),
                        'installment_count' => 0,
                        'ref_ret_num' => null,
                    ];
                }
            } catch (\Exception $e) {
                Log::error('Payment processing failed with exception', [
                    'error_message' => $e->getMessage()
                ]);

                $response = [
                    'status' => 'error',
                    'error_message' => 'Payment processing error',
                    'md_error_message' => 'Ödeme işlemi sırasında bir hata oluştu: ' . $e->getMessage(),
                    'installment_count' => 0,
                    'ref_ret_num' => null,
                ];
            }
            
            // Null check for response and ensure required keys exist
            if (!$response) {
                $response = [
                    'status' => 'error',
                    'error_message' => 'No response received from payment gateway',
                    'md_error_message' => 'Gateway returned null response',
                    'installment_count' => 0,
                    'ref_ret_num' => null,
                ];
            } else {
                // Ensure required keys exist with default values
                $response = array_merge([
                    'status' => 'error',
                    'error_message' => '',
                    'md_error_message' => '',
                    'installment_count' => 0,
                    'ref_ret_num' => null,
                ], $response);
            }
            
            $session->set('last_response', $response);
        } catch (HashMismatchException $e) {
            Log::error($e->getMessage());
            $response = array_merge($response, [
                'error_message' => 'Hash mismatch error occurred',
                'md_error_message' => $e->getMessage(),
            ]);
        } catch (\Exception|\Error $e) {
            Log::error($e->getMessage());
            $response = array_merge($response, [
                'error_message' => 'Payment processing error',
                'md_error_message' => $e->getMessage(),
            ]);
        }

        handle_payment_result:
        $payment_id = null;
        $cart_id = null;
        if (str_starts_with($order['id'], 'P')) {
            $payment_id = (int)substr($order['id'], 1);
            $payment = Payment::find($payment_id);
            if ($payment) {
                $entity_id = $payment->entity_id;
            }
        }

        if (str_starts_with($order['id'], 'C')) {
            $cart_id = (int)substr($order['id'], 1);
            $cart = Cart::find($cart_id);
            if ($cart) {
                $entity_id = $cart->entity_id;
            }
        }

        if ($payment) {
            $card_number = $payment->card_number;
            $card_holder = $payment->card_holder;
            $phone_number = $payment->phone_number;
        }

        PaymentTransaction::create([
            'payment_id' => $payment_id ?? null,
            'cart_id' => $cart_id ?? null,
            'entity_id' => $entity_id ?? null,
            'order_id' => $order['id'],
            'amount' => $order['amount'],
            'status' => $response['status'] ?? 'error',
            'md_error_message' => $response['md_error_message'] ?? '',
            'pos_name' => $session->get('selected_pos'),
            'card_holder' => $card_holder,
            'card_number' => $card_number,
            'provision_number' => $response['ref_ret_num'] ?? null,
            'installment_count' => $response['installment_count'] ?? 0,
            'response' => json_encode($response),
        ]);

        // Ödeme başarı kontrolü
        $isPaymentSuccessful = false;

        if ($session->get('selected_pos') === 'kuveytpos') {
            // KuveytPos için özel başarı kontrolü
            $posIsSuccess = false;
            try {
                $posIsSuccess = $this->pos->isSuccess();
            } catch (\Exception $e) {
                Log::warning('KuveytPos isSuccess() method failed', ['error' => $e->getMessage()]);
                $posIsSuccess = false;
            }

            $isPaymentSuccessful = (isset($response['status']) && $response['status'] === 'approved') ||
                                   $posIsSuccess ||
                                   (isset($response['response_code']) && $response['response_code'] === '00');

            Log::info('KuveytPos payment success check', [
                'final_success' => $isPaymentSuccessful
            ]);
        } else {
            // Diğer POS'lar için normal kontrol
            $isPaymentSuccessful = $this->pos->isSuccess() || (isset($response['status']) && $response['status'] === 'approved');
        }
        
        if ($isPaymentSuccessful) {
            if ($phone_number) {
                try {
                    $sms_message = 'Sayın '.$card_holder.', '.$card_number.' nolu kartınızdan bayi olan ticari borcunuza karşılık AKDAĞ YALITIM A.Ş\'ye '.Number::currency($payment->amount, 'TRY', 'tr').' ödeme yaptınız. Yaptığınız ödemenin karşılığını almadıysanız ya da ödemeyi onaylamıyorsanız bankanız ile irtibata geçerek işlemi iptal ediniz, aksi halde herhangi bir talepte bulunamayacağınızı ve şirketimizi sorumlu tutamayacağınızı bildiririz.';
                    $client = new Client();
                    $request_data = [
                        'dil' => 'tr',
                        'msgheader' => 'AKDAGYALITM',
                        'usercode' => '**********',
                        'password' => '92@BB6D',
                        'gsmno' => $phone_number,
                        'message' => $sms_message
                    ];
                    $client->get('https://api.netgsm.com.tr/sms/send/get/?' . http_build_query($request_data));
                } catch (\Exception $e) {
                    // sms gonderilemedi
                }
            }

//            $user = auth()->user();
//            $entity = Entity::find($user->active_entity_id);
//            $input = [
//                'entity_name' => $entity->entity_name,
//                'pos_name' => $session->get('selected_pos'),
//                'amount' => $order['amount'],
//                'card_holder' => $card_holder,
//                'installment_count' => $response['installment_count'],
//                'card_number' => $card_number,
//                'created_at' => now(),
//            ];
//            $emails = ['<EMAIL>'];
//            $data = [
//                'name' => "Akdag Tasyunu Portal",
//                'user' => $user,
//                'input' => $input
//            ];
//            Mail::to($emails)->queue(new NewPaymentMail($data));

            // odeme basarili, siparis durumunu guncelle
            if ($payment_id) {
                $totalPaid = PaymentTransaction::where('payment_id', $payment_id)->where('status', 'approved')->sum('amount');
                $payment = Payment::find($payment_id);
                $payment->remaining_amount = $payment->amount - $totalPaid;
                $payment->last_payment_date = date('Y-m-d');
                $payment->save();
                return redirect()->route('payment.show', ['id' => encode_id($payment_id)]);
            }

            if ($cart_id) {
                $cart = Cart::find($cart_id);
                if ($cart) {
                    return post_order_to_uyum($cart);
                }
                return redirect()->route('order.create_wizard', ['action' => 'odendi', 'id' => encode_id($cart_id)]);
            }
        }

        if ($payment_id) {
            return redirect()->route('payment.show', ['id' => encode_id($payment_id)])
                ->withErrors($response['error_message'] ?? 'Payment failed');
        }

        if ($cart_id) {
            return redirect()->route('order.create_wizard', ['action' => 'sepet'])
                ->withErrors($response['error_message'] ?? 'Payment failed');
        }

        return redirect()->route('odeme.index')->withErrors($response['error_message'] ?? 'Payment failed');
    }

    private function createNewOrder(
        string $paymentModel,
        string $callbackUrl,
        string $ip,
        string $currency,
        string $txnCode,
        ?int   $installment = 0,
        ?int   $payment_id = 0,
        ?int   $cart_id = 0,
        ?float $amount = 0.0,
        string $lang = PosInterface::LANG_TR

    ): array
    {
        if ($payment_id > 0) {
            $orderId = 'P' . $payment_id;
        } elseif ($cart_id > 0) {
            $orderId = 'C' . $cart_id;
        } else {
            $orderId = date('Ymd') . strtoupper(substr(uniqid(sha1(time())), 0, 4));
        }

        $order = [
            'id' => $orderId,
            'amount' => $amount,
            'currency' => $currency,
            'installment' => $installment,
            'txnCode' => $txnCode,
            'ip' => filter_var($ip, \FILTER_VALIDATE_IP, \FILTER_FLAG_IPV4) ? $ip : '127.0.0.1',
        ];

        if (in_array($paymentModel, [
            PosInterface::MODEL_3D_SECURE,
            PosInterface::MODEL_3D_PAY,
            PosInterface::MODEL_3D_HOST,
            PosInterface::MODEL_3D_PAY_HOSTING,
        ], true)) {
            $order['success_url'] = $callbackUrl;
            $order['fail_url'] = $callbackUrl;
        }

        if ($lang) {
            //lang degeri verilmezse account (EstPosAccount) dili kullanilacak
            $order['lang'] = $lang;
        }

        return $order;
    }

    private function createCard(PosInterface $pos, array $card): CreditCardInterface
    {
        try {
            // Get card type from form or detect it
            $providedType = $card['type'] ?? null;
            $detectedType = $this->detectCardType($card['number']);
            
            // Normalize the card type - convert form values to gateway-expected values
            $cardType = $this->normalizeCardType($providedType) ?? $detectedType;
            
            
            return CreditCardFactory::createForGateway(
                $pos,
                $card['number'],
                $card['year'],
                $card['month'],
                $card['cvv'],
                $card['name'],
                $cardType
            );
        } catch (CardTypeRequiredException|CardTypeNotSupportedException $e) {
            Log::error('Card type error', [
                'final_card_type' => $cardType ?? 'unknown',
                'card_number_prefix' => substr($card['number'], 0, 4),
                'supported_types' => array_keys($pos->getCardTypeMapping()),
                'error' => $e->getMessage()
            ]);
            throw $e;
        } catch (\LogicException $e) {
            Log::error($e);
            throw $e;
        }
    }

    private function normalizeCardType(?string $cardType): ?string
    {
        if (!$cardType) {
            return null;
        }

        // Convert form values to gateway-expected values
        $typeMapping = [
            'MasterCard' => 'master',
            'Visa' => 'visa',
            'Troy' => 'troy',
            'Amex' => null, // Not supported by KuveytPos
            'American Express' => null, // Not supported
        ];

        return $typeMapping[$cardType] ?? strtolower($cardType);
    }

    private function detectCardType(string $cardNumber): string
    {
        // Remove spaces and non-numeric characters
        $cardNumber = preg_replace('/\D/', '', $cardNumber);
        
        // Troy cards (9528, 6542, 6552, 65xx ranges)
        if (preg_match('/^9528/', $cardNumber) || 
            preg_match('/^6542/', $cardNumber) || 
            preg_match('/^6552/', $cardNumber)) {
            return 'troy';
        }
        
        // Visa cards (4xxx)
        if (preg_match('/^4/', $cardNumber)) {
            return 'visa';
        }
        
        // MasterCard (5xxx, 2xxx ranges)
        if (preg_match('/^5[1-5]/', $cardNumber) || 
            preg_match('/^2[2-7]/', $cardNumber)) {
            return 'master';
        }
        return 'visa';
    }

    private function parseKuveytPosAuthenticationResponse(string $xmlResponse, Request $request): ?array
    {
        try {
            // XML response'un boş olup olmadığını kontrol et
            if (empty(trim($xmlResponse))) {
                Log::error('KuveytPos XML response is empty');
                return null;
            }

            $dom = new \DOMDocument();
            // XML parsing hatalarını bastır
            libxml_use_internal_errors(true);

            if (!$dom->loadXML($xmlResponse)) {
                $xmlErrors = libxml_get_errors();
                Log::error('Failed to parse KuveytPos XML response', [
                    'xml_errors' => $xmlErrors,
                    'xml_content' => $xmlResponse
                ]);
                libxml_clear_errors();
                return null;
            }

            // Extract key values from XML
            $responseCode = $this->getXmlValue($dom, 'ResponseCode');
            $responseMessage = $this->getXmlValue($dom, 'ResponseMessage');
            $mdStatusCode = $this->getXmlValue($dom, 'MDStatusCode');
            $mdStatusDescription = $this->getXmlValue($dom, 'MDStatusDescription');
            $merchantOrderId = $this->getXmlValue($dom, 'MerchantOrderId');
            $orderId = $this->getXmlValue($dom, 'OrderId');
            $amount = $this->getXmlValue($dom, 'Amount');
            $installmentCount = $this->getXmlValue($dom, 'InstallmentCount');
            $businessKey = $this->getXmlValue($dom, 'BusinessKey');


            // BusinessKey kontrol et - bazen MD değeri Base64, bazen BusinessKey sayısal
            $originalBusinessKey = $businessKey;
            $mdValue = $this->getXmlValue($dom, 'MD'); // MD değerini de al
            
            // MD değeri varsa ve Base64'se onu kullan, yoksa BusinessKey'i kullan
            if (!empty($mdValue) && $this->isValidBase64($mdValue)) {
                $businessKey = $mdValue;
            } else if (!empty($businessKey)) {
                // BusinessKey sayısal ise Base64 encode et
                if (!$this->isValidBase64($businessKey)) {
                    $businessKey = base64_encode($businessKey);
                }
            }

            // Check if authentication was successful
            if ($responseCode === '00' && $mdStatusCode === '1' && $mdStatusDescription === 'AUTHENTICATION_SUCCESSFUL') {
                return [
                    'status' => 'approved',
                    'error_message' => '',
                    'md_error_message' => '',
                    'installment_count' => (int)$installmentCount ?: 0,
                    'ref_ret_num' => $businessKey,
                    'order_id' => $orderId,
                    'merchant_order_id' => $merchantOrderId,
                    'amount' => $amount,
                    'response_code' => $responseCode,
                    'response_message' => $responseMessage,
                    'md_status' => $mdStatusCode,
                    'md_status_description' => $mdStatusDescription,
                ];
            } else {
                return [
                    'status' => 'declined',
                    'error_message' => 'Payment declined by bank',
                    'md_error_message' => $responseMessage . ' (Code: ' . $responseCode . ')',
                    'installment_count' => 0,
                    'ref_ret_num' => null,
                    'response_code' => $responseCode,
                    'response_message' => $responseMessage,
                ];
            }

        } catch (\Exception $e) {
            Log::error('Error parsing KuveytPos AuthenticationResponse', [
                'error' => $e->getMessage(),
                'xml' => $xmlResponse
            ]);
            return null;
        }
    }

    private function getXmlValue(\DOMDocument $dom, string $tagName): ?string
    {
        try {
            $nodes = $dom->getElementsByTagName($tagName);
            if ($nodes && $nodes->length > 0) {
                $node = $nodes->item(0);
                if ($node && $node->textContent !== null) {
                    return trim($node->textContent);
                }
            }
        } catch (\Exception $e) {
            Log::warning('Error extracting XML value', [
                'tag_name' => $tagName,
                'error' => $e->getMessage()
            ]);
        }
        return null;
    }

    /**
     * KuveytPos için tamamen manuel işlem
     */
    private function processKuveytPosManually(Request $request, $session): ?array
    {
        try {

            $order = $session->get('order');
            if (!$order) {
                Log::error('Order not found in session');
                return null;
            }

            // AuthenticationResponse varsa işle
            if ($request->has('AuthenticationResponse')) {
                $authResponse = urldecode($request->get('AuthenticationResponse'));
                $parsedAuth = $this->parseKuveytPosAuthenticationResponse($authResponse, $request);

                if ($parsedAuth && $parsedAuth['status'] === 'approved') {
                    return $this->makeKuveytPosProvisionRequest($parsedAuth, $order, $request);
                } else {
                    return [
                        'status' => 'declined',
                        'error_message' => 'Authentication failed',
                        'md_error_message' => '3D Secure doğrulama başarısız oldu.',
                        'installment_count' => 0,
                        'ref_ret_num' => null,
                    ];
                }
            }

            // Direkt POST parametreleri varsa işle
            $responseCode = $request->get('ResponseCode');
            $responseMessage = $request->get('ResponseMessage');
            $md = $request->get('MD');
            $merchantOrderId = $request->get('MerchantOrderId');
            $orderId = $request->get('OrderId');
            $amount = $request->get('Amount');

            if ($responseCode && $merchantOrderId) {
                if ($responseCode === '00') {
                    return [
                        'status' => 'approved',
                        'error_message' => '',
                        'md_error_message' => '',
                        'installment_count' => (int)$request->get('InstallmentCount', 0),
                        'ref_ret_num' => $md,
                        'order_id' => $orderId,
                        'merchant_order_id' => $merchantOrderId,
                        'amount' => $amount ?: $order['amount'],
                        'response_code' => $responseCode,
                        'response_message' => $responseMessage,
                    ];
                } else {
                    return [
                        'status' => 'declined',
                        'error_message' => 'Payment declined by bank',
                        'md_error_message' => $responseMessage . ' (Code: ' . $responseCode . ')',
                        'installment_count' => 0,
                        'ref_ret_num' => null,
                        'response_code' => $responseCode,
                        'response_message' => $responseMessage,
                    ];
                }
            }

            Log::warning('KuveytPos: No recognizable response format found');
            return null;

        } catch (\Exception $e) {
            Log::error('KuveytPos manual processing failed', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * KuveytPos XML hatası durumunda manuel response işleme
     */
    private function handleKuveytPosManualResponse(Request $request, array $order): ?array
    {
        try {

            // AuthenticationResponse varsa onu kontrol et (öncelik ver)
            if ($request->has('AuthenticationResponse')) {
                $authResponse = urldecode($request->get('AuthenticationResponse'));
                $parsedAuth = $this->parseKuveytPosAuthenticationResponse($authResponse, $request);

                if ($parsedAuth && $parsedAuth['status'] === 'approved') {
                    // 3D doğrulama başarılı, şimdi provision isteği gönder
                    return $this->makeKuveytPosProvisionRequest($parsedAuth, $order, $request);
                }
            }

            // Request'ten gerekli verileri al (fallback)
            $md = $request->get('MD');
            $merchantOrderId = $request->get('MerchantOrderId');
            $orderId = $request->get('OrderId');
            $amount = $request->get('Amount');
            $responseCode = $request->get('ResponseCode');
            $responseMessage = $request->get('ResponseMessage');
            $installmentCount = $request->get('InstallmentCount', 0);

            // Direkt response parametreleri varsa bunları kullan
            if ($responseCode && $merchantOrderId) {
                if ($responseCode === '00') {
                    // Başarılı response
                    return [
                        'status' => 'approved',
                        'error_message' => '',
                        'md_error_message' => '',
                        'installment_count' => (int)$installmentCount,
                        'ref_ret_num' => $md,
                        'order_id' => $orderId,
                        'merchant_order_id' => $merchantOrderId,
                        'amount' => $amount,
                        'response_code' => $responseCode,
                        'response_message' => $responseMessage,
                    ];
                } else {
                    // Başarısız response
                    return [
                        'status' => 'declined',
                        'error_message' => 'Payment declined by bank',
                        'md_error_message' => $responseMessage . ' (Code: ' . $responseCode . ')',
                        'installment_count' => 0,
                        'ref_ret_num' => null,
                        'response_code' => $responseCode,
                        'response_message' => $responseMessage,
                    ];
                }
            }

            Log::warning('KuveytPos manual: Insufficient data for manual processing');
            return null;

        } catch (\Exception $e) {
            Log::error('KuveytPos manual response processing failed', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return null;
        }
    }

    /**
     * KuveytPos için provision (ikinci istek) gönder
     */
    private function makeKuveytPosProvisionRequest(array $authResponse, array $order, Request $request): ?array
    {
        try {

            // KuveytPos provision için gerekli parametreler
            $provisionData = [
                'MerchantId' => config('laravel-pos.banks.kuveytpos.credentials.merchant_id'),
                'CustomerId' => config('laravel-pos.banks.kuveytpos.credentials.terminal_id'),
                'UserName' => config('laravel-pos.banks.kuveytpos.credentials.user_name'),
                'Password' => config('laravel-pos.banks.kuveytpos.credentials.enc_key'),
                'OrderId' => $authResponse['order_id'] ?? $order['id'],
                'MerchantOrderId' => $authResponse['merchant_order_id'] ?? $order['id'],
                'Amount' => $authResponse['amount'] ?? $order['amount'],
                'InstallmentCount' => $authResponse['installment_count'] ?? 0,
                'BusinessKey' => $authResponse['ref_ret_num'],
                'TransactionType' => 'Sale',
            ];


            // XML request oluştur
            $xmlRequest = $this->buildKuveytPosProvisionXml($provisionData);


            // HTTP client ile provision endpoint'ine istek gönder
            $client = new \GuzzleHttp\Client();
            
            // KuveytPos için doğru provision URL'i kullan
            // Test/production environment'a göre URL seç
            $isTestMode = config('laravel-pos.banks.kuveytpos.test_mode', true);
            if ($isTestMode) {
                $provisionUrl = 'https://boatest.kuveytturk.com.tr/boa.virtualpos.services/Home/ThreeDModelProvisionGate';
            } else {
                $provisionUrl = 'https://sanalpos.kuveytturk.com.tr/ServiceGateWay/Home/ThreeDModelProvisionGate';
            }


            $response = $client->post($provisionUrl, [
                'headers' => [
                    'Content-Type' => 'text/xml; charset=UTF-8',
                    'Accept' => 'text/xml, application/xml',
                    'User-Agent' => 'KuveytPos PHP Client',
                ],
                'body' => $xmlRequest,
                'timeout' => 30,
                'verify' => false, // SSL doğrulamasını devre dışı bırak
                'allow_redirects' => false,
                'http_errors' => true,
            ]);

            $responseBody = $response->getBody()->getContents();

            // XML response'u parse et
            return $this->parseKuveytPosProvisionResponse($responseBody);

        } catch (\GuzzleHttp\Exception\RequestException $e) {
            $responseBody = '';
            $statusCode = 'No status';
            
            if ($e->hasResponse()) {
                $responseBody = $e->getResponse()->getBody()->getContents();
                $statusCode = $e->getResponse()->getStatusCode();
                
                // 500 hatası durumunda hata kaydet
                if ($statusCode == 500) {
                    Log::error('KuveytPos provision server returned 500 error', [
                        'error_message' => $e->getMessage()
                    ]);
                }
            }
            
            Log::error('KuveytPos provision HTTP request failed', [
                'error' => $e->getMessage(),
                'status_code' => $statusCode
            ]);
            return null;
        } catch (\Exception $e) {
            Log::error('KuveytPos provision request failed', [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * KuveytPos provision XML request oluştur (Resmi dokümana göre)
     */
    private function buildKuveytPosProvisionXml(array $data): string
    {
        // KuveytTurk resmi dokümanına göre hash hesapla
        $hashData = $this->calculateKuveytPosProvisionHash($data);

        // BusinessKey (MD) değerini Base64 formatında kontrol et ve düzelt
        $businessKey = $data['BusinessKey'];
        if (!empty($businessKey)) {
            // Base64 doğrulaması yap
            if (!$this->isValidBase64($businessKey)) {
                // Base64 padding eksikse ekle
                $businessKey = $this->fixBase64Padding($businessKey);
                
                // Hala geçerli değilse encode et
                if (!$this->isValidBase64($businessKey)) {
                    $businessKey = base64_encode($businessKey);
                }
            }
        }

        // KuveytTurk provision formatı - Resmi dokümandaki sıralama
        $xml = '<?xml version="1.0" encoding="utf-8"?>';
        $xml .= '<KuveytTurkVPosMessage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">';
        $xml .= '<APIVersion>TDV2.0.0</APIVersion>';
        $xml .= '<HashData>' . htmlspecialchars($hashData) . '</HashData>';
        $xml .= '<MerchantId>' . htmlspecialchars($data['MerchantId']) . '</MerchantId>';
        $xml .= '<CustomerId>' . htmlspecialchars($data['CustomerId']) . '</CustomerId>';
        $xml .= '<UserName>' . htmlspecialchars($data['UserName']) . '</UserName>';
        $xml .= '<TransactionType>' . htmlspecialchars($data['TransactionType']) . '</TransactionType>';
        $xml .= '<InstallmentCount>' . htmlspecialchars($data['InstallmentCount']) . '</InstallmentCount>';
        $xml .= '<Amount>' . htmlspecialchars($data['Amount']) . '</Amount>';
        $xml .= '<MerchantOrderId>' . htmlspecialchars($data['MerchantOrderId']) . '</MerchantOrderId>';
        $xml .= '<TransactionSecurity>3</TransactionSecurity>';
        $xml .= '<KuveytTurkVPosAdditionalData>';
        $xml .= '<AdditionalData>';
        $xml .= '<Key>MD</Key>';
        $xml .= '<Data>' . htmlspecialchars($businessKey) . '</Data>';
        $xml .= '</AdditionalData>';
        $xml .= '</KuveytTurkVPosAdditionalData>';
        $xml .= '</KuveytTurkVPosMessage>';


        return $xml;
    }

    /**
     * KuveytPos provision için hash hesapla (mewebstudio/pos paketi ile aynı yöntem)
     */
    private function calculateKuveytPosProvisionHash(array $data): string
    {
        // mewebstudio/pos paketindeki KuveytPosCrypt::createHash metodunun aynısı
        
        // 1. Password'u hash'le (binary format)
        $password = $data['Password'];
        $hashedPassword = base64_encode(sha1($password, true));

        // 2. Hash string oluştur - mewebstudio/pos formatı
        // MerchantId + MerchantOrderId + Amount + (OkUrl - boş) + (FailUrl - boş) + UserName + HashedPassword
        $hashData = [
            $data['MerchantId'],
            $data['MerchantOrderId'],
            $data['Amount'],
            '', // OkUrl (boş - provision request'te yok)
            '', // FailUrl (boş - provision request'te yok)
            $data['UserName'],
            $hashedPassword
        ];

        // 3. Separator ile birleştir ve final hash hesapla
        $hashString = implode('', $hashData); // KuveytPos separator kullanmıyor
        $finalHash = base64_encode(sha1($hashString, true));


        return $finalHash;
    }

    /**
     * KuveytPos için hash hesapla (Eski metod - kullanılmıyor)
     */
    private function calculateKuveytPosHash(array $data): string
    {
        // KuveytTurk provision için farklı hash formatları dene

        // Format 1: Standart format
        $hashString1 = $data['MerchantId'] .
                      $data['MerchantOrderId'] .
                      $data['Amount'] .
                      '' . // OkUrl (boş)
                      '' . // FailUrl (boş)
                      $data['UserName'] .
                      $data['Password'];

        // Format 2: Sadece temel alanlar
        $hashString2 = $data['MerchantId'] .
                      $data['MerchantOrderId'] .
                      $data['Amount'] .
                      $data['UserName'] .
                      $data['Password'];

        // Format 3: BusinessKey dahil
        $hashString3 = $data['MerchantId'] .
                      $data['MerchantOrderId'] .
                      $data['Amount'] .
                      $data['BusinessKey'] .
                      $data['UserName'] .
                      $data['Password'];

        // Farklı hash algoritmaları dene
        $hash1 = base64_encode(sha1($hashString1, true));
        $hash2 = base64_encode(sha1($hashString2, true));
        $hash3 = base64_encode(sha1($hashString3, true));
        $hash4 = base64_encode(hash('sha256', $hashString1, true));
        $hash5 = sha1($hashString1); // Hex format


        // İlk olarak standart formatı dene
        return $hash1;
    }

    /**
     * KuveytPos provision response'u parse et
     */
    private function parseKuveytPosProvisionResponse(string $xmlResponse): ?array
    {
        try {
            if (empty(trim($xmlResponse))) {
                Log::error('KuveytPos provision response is empty');
                return null;
            }

            $dom = new \DOMDocument();
            libxml_use_internal_errors(true);

            if (!$dom->loadXML($xmlResponse)) {
                $xmlErrors = libxml_get_errors();
                Log::error('Failed to parse KuveytPos provision response', [
                    'xml_errors' => $xmlErrors,
                    'xml_content' => $xmlResponse
                ]);
                libxml_clear_errors();
                return null;
            }

            // Provision response'dan değerleri çıkar
            $responseCode = $this->getXmlValue($dom, 'ResponseCode');
            $responseMessage = $this->getXmlValue($dom, 'ResponseMessage');
            $orderId = $this->getXmlValue($dom, 'OrderId');
            $merchantOrderId = $this->getXmlValue($dom, 'MerchantOrderId');
            $amount = $this->getXmlValue($dom, 'Amount');
            $provisionNumber = $this->getXmlValue($dom, 'ProvisionNumber');
            $rrn = $this->getXmlValue($dom, 'RRN');
            $stan = $this->getXmlValue($dom, 'Stan');
            $installmentCount = $this->getXmlValue($dom, 'InstallmentCount');


            // Başarılı provision kontrolü
            if ($responseCode === '00') {

                return [
                    'status' => 'approved',
                    'error_message' => '',
                    'md_error_message' => '',
                    'installment_count' => (int)$installmentCount ?: 0,
                    'ref_ret_num' => $provisionNumber ?: $rrn,
                    'order_id' => $orderId,
                    'merchant_order_id' => $merchantOrderId,
                    'amount' => $amount,
                    'response_code' => $responseCode,
                    'response_message' => $responseMessage,
                    'provision_number' => $provisionNumber,
                    'rrn' => $rrn,
                    'stan' => $stan,
                ];
            } else {

                return [
                    'status' => 'declined',
                    'error_message' => 'Payment declined by bank',
                    'md_error_message' => $responseMessage . ' (Code: ' . $responseCode . ')',
                    'installment_count' => 0,
                    'ref_ret_num' => null,
                    'response_code' => $responseCode,
                    'response_message' => $responseMessage,
                ];
            }

        } catch (\Exception $e) {
            Log::error('Error parsing KuveytPos provision response', [
                'error' => $e->getMessage(),
                'xml' => $xmlResponse
            ]);
            return null;
        }
    }

    /**
     * Base64 formatının geçerli olup olmadığını kontrol et
     */
    private function isValidBase64(string $data): bool
    {
        // Boş string kontrolü
        if (empty($data)) {
            return false;
        }
        
        // Base64 karakterler dışında karakter var mı kontrol et
        if (!preg_match('/^[a-zA-Z0-9\/\r\n+]*={0,2}$/', $data)) {
            return false;
        }
        
        // Base64 decode işlemini dene
        $decoded = base64_decode($data, true);
        
        // Decode başarısız olursa false döndür
        if ($decoded === false) {
            return false;
        }
        
        // Tekrar encode ederek orijinali ile karşılaştır
        if (base64_encode($decoded) === $data) {
            return true;
        }
        
        return false;
    }

    /**
     * Base64 padding'ini düzelt
     */
    private function fixBase64Padding(string $data): string
    {
        // Whitespace karakterleri temizle
        $data = preg_replace('/\s/', '', $data);
        
        // Base64 padding için = karakterlerini ekle
        $remainder = strlen($data) % 4;
        if ($remainder) {
            $padding = 4 - $remainder;
            $data .= str_repeat('=', $padding);
        }
        
        return $data;
    }

}