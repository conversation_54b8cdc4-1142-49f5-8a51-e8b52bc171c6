<?php

namespace App\Console\Commands;

use App\Models\Contract;
use App\Models\ContractExpense;
use Illuminate\Console\Command;

class ContractAmountsYedek extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:contract-amounts';

    /**
     * The console command description.
     */
    protected $description = 'Get contract expenses.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $contracts = Contract::select('entity_id')->distinct()->groupBy('entity_id')->where('ispassive', 0)->get();
        foreach ($contracts as $contract) {
            $amounts = uyumapi('SELECT 
     sm.form_contract_m_id AS "contract_id",
    COALESCE(
        (SELECT SUM(    
            CASE 
                WHEN b.purchase_sales IN (1, 3) THEN (a.amt_with_disc + a.amt_vat) * -1 
                ELSE (a.amt_with_disc + a.amt_vat) 
            END
        ) 
        FROM invt_item_d a 
        LEFT JOIN invt_item_m b ON b.item_m_id = a.item_m_id 
        WHERE a.form_contract_m_id = sm.form_contract_m_id
        ), 0
    ) - 
    COALESCE(
        (SELECT SUM(
            CASE 
                WHEN b.purchase_sales IN (1, 3) THEN (a.amt_with_disc + a.amt_vat) 
                ELSE 0 
            END
        ) 
        FROM psmt_invoice_d a 
        LEFT JOIN psmt_invoice_m b ON b.invoice_m_id = a.invoice_m_id 
        WHERE a.form_contract_m_id = sm.form_contract_m_id
        ), 0
    ) AS "shipping_amount",
    COALESCE(
        (SELECT ROUND(SUM(((a.amt_with_disc + a.amt_vat) / a.qty) * (a.qty - a.qty_shipping)), 2)
        FROM psmt_order_d a 
        LEFT JOIN psmt_order_m b ON b.order_m_id = a.order_m_id 
        WHERE a.order_status = 1 AND a.purchase_sales = 2 AND a.form_contract_m_id = sm.form_contract_m_id
        ), 0
    ) AS "order_amount",
    (sm.amt + 
    COALESCE(
        (SELECT SUM(
            CASE 
                WHEN b.purchase_sales IN (1, 3) THEN (a.amt_with_disc + a.amt_vat) 
                ELSE 0 
            END
        ) 
        FROM psmt_invoice_d a 
        LEFT JOIN psmt_invoice_m b ON b.invoice_m_id = a.invoice_m_id 
        WHERE a.form_contract_m_id = sm.form_contract_m_id
        ), 0
    )) - 
    (COALESCE(
        (SELECT SUM(
            CASE 
                WHEN b.purchase_sales IN (1, 3) THEN (a.amt_with_disc + a.amt_vat) * -1 
                ELSE (a.amt_with_disc + a.amt_vat) 
            END
        ) 
        FROM invt_item_d a 
        LEFT JOIN invt_item_m b ON b.item_m_id = a.item_m_id 
        WHERE a.form_contract_m_id = sm.form_contract_m_id
        ), 0
    ) + 
    COALESCE(
        (SELECT ROUND(SUM(((a.amt_with_disc + a.amt_vat) / a.qty) * (a.qty - a.qty_shipping)), 2)
        FROM psmt_order_d a 
        LEFT JOIN psmt_order_m b ON b.order_m_id = a.order_m_id 
        WHERE a.order_status = 1 AND a.purchase_sales = 2 AND a.form_contract_m_id = sm.form_contract_m_id
        ), 0
    )) AS "remaining_amount"
FROM fint_form_contract_m sm 
LEFT JOIN find_entity ck ON ck.entity_id = sm.entity_id
WHERE  ck.entity_id = ' . $contract->entity_id . '
  AND sm.ispassive = 0');
            foreach ($amounts as $amount) {
                $contract = Contract::find($amount['contract_id']);
                $contract->timestamps = false;
                $contract->shipping_amount = $amount['shipping_amount'];
                $contract->order_amount = $amount['order_amount'];
                $contract->remaining_amount = $amount['remaining_amount'];
                $contract->save();

            }
        }
    }
}
