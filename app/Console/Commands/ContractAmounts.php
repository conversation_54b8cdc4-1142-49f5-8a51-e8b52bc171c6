<?php

namespace App\Console\Commands;

use App\Models\Contract;
use App\Models\ContractExpense;
use Illuminate\Console\Command;

class ContractAmounts extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:contract-amounts';

    /**
     * The console command description.
     */
    protected $description = 'Get contract expenses.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info('Contract amounts güncelleniyor...');

        // Tüm aktif entity_id'leri al
        $entityIds = Contract::select('entity_id')
            ->distinct()
            ->where('ispassive', 0)
            ->pluck('entity_id')
            ->toArray();

        if (empty($entityIds)) {
            $this->info('Güncellenecek contract bulunamadı.');
            return;
        }

        // Entity ID'leri chunk'lara böl (50'şer 50'şer işle)
        $chunks = array_chunk($entityIds, 50);

        foreach ($chunks as $chunkIndex => $chunk) {
            $this->info('Chunk ' . ($chunkIndex + 1) . '/' . count($chunks) . ' işleniyor...');

            try {
                // Tek API çağrısı ile tüm chunk'ı işle
                $entityIdList = implode(',', $chunk);

                $amounts = uyumapi('SELECT 
         sm.form_contract_m_id AS "contract_id",
        COALESCE(
            (SELECT SUM(    
                CASE 
                    WHEN b.purchase_sales IN (1, 3) THEN (a.amt_with_disc + a.amt_vat) * -1 
                    ELSE (a.amt_with_disc + a.amt_vat) 
                END
            ) 
            FROM invt_item_d a 
            LEFT JOIN invt_item_m b ON b.item_m_id = a.item_m_id 
            WHERE a.form_contract_m_id = sm.form_contract_m_id
            ), 0
        ) - 
        COALESCE(
            (SELECT SUM(
                CASE 
                    WHEN b.purchase_sales IN (1, 3) THEN (a.amt_with_disc + a.amt_vat) 
                    ELSE 0 
                END
            ) 
            FROM psmt_invoice_d a 
            LEFT JOIN psmt_invoice_m b ON b.invoice_m_id = a.invoice_m_id 
            WHERE a.form_contract_m_id = sm.form_contract_m_id
            ), 0
        ) AS "shipping_amount",
        COALESCE(
            (SELECT ROUND(SUM(((a.amt_with_disc + a.amt_vat) / a.qty) * (a.qty - a.qty_shipping)), 2)
            FROM psmt_order_d a 
            LEFT JOIN psmt_order_m b ON b.order_m_id = a.order_m_id 
            WHERE a.order_status = 1 AND a.purchase_sales = 2 AND a.form_contract_m_id = sm.form_contract_m_id
            ), 0
        ) AS "order_amount",
        (sm.amt + 
        COALESCE(
            (SELECT SUM(
                CASE 
                    WHEN b.purchase_sales IN (1, 3) THEN (a.amt_with_disc + a.amt_vat) 
                    ELSE 0 
                END
            ) 
            FROM psmt_invoice_d a 
            LEFT JOIN psmt_invoice_m b ON b.invoice_m_id = a.invoice_m_id 
            WHERE a.form_contract_m_id = sm.form_contract_m_id
            ), 0
        )) - 
        (COALESCE(
            (SELECT SUM(
                CASE 
                    WHEN b.purchase_sales IN (1, 3) THEN (a.amt_with_disc + a.amt_vat) * -1 
                    ELSE (a.amt_with_disc + a.amt_vat) 
                END
            ) 
            FROM invt_item_d a 
            LEFT JOIN invt_item_m b ON b.item_m_id = a.item_m_id 
            WHERE a.form_contract_m_id = sm.form_contract_m_id
            ), 0
        ) + 
        COALESCE(
            (SELECT ROUND(SUM(((a.amt_with_disc + a.amt_vat) / a.qty) * (a.qty - a.qty_shipping)), 2)
            FROM psmt_order_d a 
            LEFT JOIN psmt_order_m b ON b.order_m_id = a.order_m_id 
            WHERE a.order_status = 1 AND a.purchase_sales = 2 AND a.form_contract_m_id = sm.form_contract_m_id
            ), 0
        )) AS "remaining_amount"
    FROM fint_form_contract_m sm 
    LEFT JOIN find_entity ck ON ck.entity_id = sm.entity_id
    WHERE  ck.entity_id IN (' . $entityIdList . ')
      AND sm.ispassive = 0', 120); // 120 saniye timeout
dd($amounts);
                // Bulk update için hazırlık
                $updateData = [];
                foreach ($amounts as $amount) {
                    $updateData[$amount['contract_id']] = [
                        'shipping_amount' => $amount['shipping_amount'],
                        'order_amount' => $amount['order_amount'],
                        'remaining_amount' => $amount['remaining_amount']
                    ];
                }

                // Bulk update
                foreach ($updateData as $contractId => $data) {
                    Contract::where('id', $contractId)->update($data);
                }

                $this->info('Chunk ' . ($chunkIndex + 1) . ' başarıyla güncellendi. (' . count($updateData) . ' contract)');

                // API rate limiting için kısa bekleme
                sleep(1);

            } catch (\Exception $e) {
                $this->error('Chunk ' . ($chunkIndex + 1) . ' hatası: ' . $e->getMessage());
                continue; // Diğer chunk'ları işlemeye devam et
            }
        }

        $this->info('Contract amounts güncelleme tamamlandı.');
    }
}
