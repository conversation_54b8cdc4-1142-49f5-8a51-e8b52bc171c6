<?php

namespace App\Console\Commands;

use App\Models\OrderD;
use App\Models\Order;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrdersCommand extends Command
{
    protected $signature = 'get:orders {--chunk=1000 : Chunk size for processing}';
    protected $description = 'Sync orders from external API';

    // Order status sabitleri
    const STATUS_CLOSED = 1;
    const STATUS_OPEN = 2;

    private $processedOrderIds = [];
    private $stats = [
        'created' => 0,
        'updated' => 0,
        'closed' => 0,
        'deleted' => 0,
        'errors' => 0
    ];

    public function handle()
    {
        $startTime = microtime(true);
        $chunkSize = $this->option('chunk');

        $this->info('Starting order synchronization...');

        try {
            // 1. Yeni ve güncellenmiş siparişleri al
            $this->syncNewAndUpdatedOrders($chunkSize);

            // 2. <PERSON><PERSON>an siparişleri kontrol et
            $this->checkClosedOrders($chunkSize);

            // 3. <PERSON>linen siparişleri temizle
            $this->removeDeletedOrders();

            // 4. İstatistikleri göster
            $this->displayStats($startTime);

        } catch (\Exception $e) {
            $this->error('Error occurred: ' . $e->getMessage());
            Log::error('Order sync error', ['exception' => $e]);
            return 1;
        }

        return 0;
    }

    private function syncNewAndUpdatedOrders($chunkSize)
    {
        $this->info('Syncing new and updated orders...');

        // Son güncelleme tarihini al
        $lastSync = Order::max('updated_at') ?? '2000-01-01';

        $offset = 0;
        $hasMore = true;

        while ($hasMore) {
            // API'den chunk halinde veri al
            $query = "
                SELECT order_m_id, doc_no, doc_date, address1, amt, cur_tra_id, 
                       form_contract_m_id, entity_id, purchase_sales, doc_tra_id, 
                       co_id, sales_person_id, branch_id, payment_method_id, amt_vat, incoterms_id,
                       country_id, order_status, request_status, city_id, create_date, update_date, 
                       town_id, shipping_date, note3
                FROM psmt_order_m
                WHERE purchase_sales = 2 
                  AND co_id = 2725
                  AND (update_date > '{$lastSync}' OR create_date > '{$lastSync}')
                ORDER BY update_date DESC
                LIMIT {$chunkSize} OFFSET {$offset}
            ";

            $orders = uyumapi($query);

            if (empty($orders)) {
                $hasMore = false;
                continue;
            }

            // Toplu işlem için veri hazırla
            $bulkData = [];

            foreach ($orders as $order) {
                $this->processedOrderIds[] = $order['order_m_id'];

                $orderData = $this->prepareOrderData($order);
                $bulkData[] = $orderData;
            }

            // Toplu güncelleme/ekleme
            $this->bulkUpsert($bulkData);

            $offset += $chunkSize;
            $this->info("Processed {$offset} orders...");

            // API'yi yormamak için kısa bekleme
            usleep(100000); // 0.1 saniye
        }
    }

    private function checkClosedOrders($chunkSize)
    {
        $this->info('Checking for closed orders...');

        // Açık siparişleri (order_status = 2) chunk halinde kontrol et
        Order::where('order_status', self::STATUS_OPEN)
            ->chunk($chunkSize, function ($orders) {
                $orderIds = $orders->pluck('id')->toArray();

                if (empty($orderIds)) {
                    return;
                }

                // API'den bu siparişlerin durumunu kontrol et
                $idList = implode(',', $orderIds);
                $query = "
                    SELECT order_m_id, order_status
                    FROM psmt_order_m
                    WHERE order_m_id IN ({$idList})
                ";

                $apiOrders = uyumapi($query);

                $statusMap = [];
                foreach ($apiOrders as $apiOrder) {
                    $statusMap[$apiOrder['order_m_id']] = $apiOrder['order_status'];
                    $this->processedOrderIds[] = $apiOrder['order_m_id'];
                }

                // Kapanan siparişleri güncelle
                foreach ($orders as $order) {
                    if (isset($statusMap[$order->id]) &&
                        $statusMap[$order->id] == self::STATUS_CLOSED &&
                        $order->order_status == self::STATUS_OPEN) {

                        $order->update(['order_status' => self::STATUS_CLOSED]);
                        $this->stats['closed']++;

                        $this->info("Order #{$order->id} ({$order->doc_no}) closed.");
                    }
                }
            });
    }

    private function removeDeletedOrders()
    {
        $this->info('Removing deleted orders...');

        // Tüm aktif order ID'lerini API'den al
        $activeOrderIds = [];
        $offset = 0;
        $chunkSize = 5000;

        while (true) {
            $query = "
                SELECT order_m_id 
                FROM psmt_order_m 
                WHERE purchase_sales = 2 AND co_id = 2725
                LIMIT {$chunkSize} OFFSET {$offset}
            ";

            $orders = uyumapi($query);

            if (empty($orders)) {
                break;
            }

            foreach ($orders as $order) {
                $activeOrderIds[] = $order['order_m_id'];
            }

            $offset += $chunkSize;
        }

        // Veritabanında olup API'de olmayan siparişleri bul
        $deletedOrders = Order::whereNotIn('id', $activeOrderIds)
            ->pluck('id')
            ->toArray();

        if (!empty($deletedOrders)) {
            DB::transaction(function () use ($deletedOrders) {
                // Önce detayları sil
                OrderD::whereIn('order_m_id', $deletedOrders)->delete();

                // Sonra ana kayıtları sil
                $deleted = Order::whereIn('id', $deletedOrders)->delete();

                $this->stats['deleted'] = $deleted;
            });
        }
    }

    private function prepareOrderData($order)
    {
        $id = $order['order_m_id'];
        unset($order['order_m_id']);

        // Tarih düzenlemeleri
        $order['shipping_date'] = $this->normalizeDate($order['shipping_date']);
        $order['created_at'] = $this->normalizeDate($order['create_date'] ?? null);
        $order['updated_at'] = $this->normalizeDate($order['update_date'] ?? null);

        // Field temizlikleri
        unset($order['create_date']);
        unset($order['update_date']);

        // Null kontrolleri
        $order['form_contract_m_id'] = $order['form_contract_m_id'] == 0 ? null : $order['form_contract_m_id'];

        // ID'yi ekle
        $order['id'] = $id;

        return $order;
    }

    private function normalizeDate($date)
    {
        if (empty($date) || $date == '0001-01-01T00:00:00') {
            return null;
        }
        return $date;
    }

    private function bulkUpsert($data)
    {
        if (empty($data)) {
            return;
        }

        // Her chunk için transaction kullan
        DB::transaction(function () use ($data) {
            foreach ($data as $orderData) {
                $id = $orderData['id'];
                unset($orderData['id']);

                $exists = Order::where('id', $id)->exists();

                if ($exists) {
                    Order::where('id', $id)->update($orderData);
                    $this->stats['updated']++;
                } else {
                    $orderData['id'] = $id;
                    Order::create($orderData);
                    $this->stats['created']++;
                }
            }
        });
    }

    private function displayStats($startTime)
    {
        $duration = round(microtime(true) - $startTime, 2);

        $this->info('');
        $this->info('=== Synchronization Complete ===');
        $this->info("Duration: {$duration} seconds");
        $this->info("Created: {$this->stats['created']} orders");
        $this->info("Updated: {$this->stats['updated']} orders");
        $this->info("Closed: {$this->stats['closed']} orders");
        $this->info("Deleted: {$this->stats['deleted']} orders");

        if ($this->stats['errors'] > 0) {
            $this->warn("Errors: {$this->stats['errors']}");
        }
    }
}