<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Town extends Model
{
    protected $fillable = ['id', 'town_name', 'city_id'];
    public $timestamps = false;
    public static function forDropdown($city_id)
    {

        $dropdown = Town::where('city_id',$city_id)->orderBy('town_name')->pluck('town_name as name', 'id');

        return $dropdown;
    }

    public function city()
    {
        return $this->belongsTo(City::class);
    }
}
