<?php

/**
 * KuveytTurk POS Final Test - <PERSON><PERSON><PERSON>öre
 * 
 * Bu script, KuveytTurk'ün resmi dokümanındaki hash hesaplamasını test eder.
 */

echo "=== KuveytTurk POS Final Test - <PERSON><PERSON><PERSON>öre ===\n\n";

// Test 1: Official Hash Calculation
echo "Test 1: Official Hash Calculation\n";
echo "----------------------------------\n";

function calculateOfficialHash(array $data): string
{
    // Resmi dokümana göre hash hesaplaması
    
    // 1. Password'u önce hash'le
    $password = $data['Password'];
    $hashedPassword = base64_encode(sha1($password, true));
    
    echo "Step 1 - Password Hashing:\n";
    echo "  Original Password: $password\n";
    echo "  SHA-1 (hex): " . sha1($password) . "\n";
    echo "  SHA-1 (base64): $hashedPassword\n\n";
    
    // 2. Request 2 için hash string: MerchantId + MerchantOrderId + Amount + UserName + HashedPassword
    $hashString = $data['MerchantId'] . 
                 $data['MerchantOrderId'] . 
                 $data['Amount'] . 
                 $data['UserName'] . 
                 $hashedPassword;
    
    echo "Step 2 - Hash String Construction:\n";
    echo "  MerchantId: " . $data['MerchantId'] . "\n";
    echo "  MerchantOrderId: " . $data['MerchantOrderId'] . "\n";
    echo "  Amount: " . $data['Amount'] . "\n";
    echo "  UserName: " . $data['UserName'] . "\n";
    echo "  HashedPassword: $hashedPassword\n";
    echo "  Combined: $hashString\n\n";
    
    // 3. Final hash hesapla
    $finalHash = base64_encode(sha1($hashString, true));
    
    echo "Step 3 - Final Hash:\n";
    echo "  SHA-1 (hex): " . sha1($hashString) . "\n";
    echo "  SHA-1 (base64): $finalHash\n";
    echo "  Hash Length: " . strlen($finalHash) . " characters\n\n";
    
    return $finalHash;
}

// Test verileri (gerçek veriler)
$testData = [
    'MerchantId' => '588693',
    'MerchantOrderId' => 'P598',
    'Amount' => '2000',
    'UserName' => 'kwebservis',
    'Password' => '3GgejhjaHCSX55k'
];

echo "Test Verileri:\n";
foreach ($testData as $key => $value) {
    echo "  $key: $value\n";
}
echo "\n";

$officialHash = calculateOfficialHash($testData);
echo "✓ Official hash hesaplandı: $officialHash\n\n";

// Test 2: Hash Validation
echo "Test 2: Hash Validation\n";
echo "------------------------\n";

// Base64 validation
if (base64_encode(base64_decode($officialHash, true)) === $officialHash) {
    echo "✓ Hash geçerli Base64 formatında\n";
} else {
    echo "✗ Hash geçersiz Base64 formatında\n";
}

// Length validation
if (strlen($officialHash) > 0 && strlen($officialHash) % 4 === 0) {
    echo "✓ Hash uzunluğu uygun (" . strlen($officialHash) . " karakter)\n";
} else {
    echo "✗ Hash uzunluğu uygun değil\n";
}

// Test 3: XML Generation
echo "\nTest 3: XML Generation\n";
echo "----------------------\n";

function buildOfficialXml(array $data, string $hash): string
{
    $xml = '<?xml version="1.0" encoding="utf-8"?>';
    $xml .= '<KuveytTurkVPosMessage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">';
    $xml .= '<APIVersion>TDV2.0.0</APIVersion>';
    $xml .= '<HashData>' . htmlspecialchars($hash) . '</HashData>';
    $xml .= '<MerchantId>' . htmlspecialchars($data['MerchantId']) . '</MerchantId>';
    $xml .= '<CustomerId>' . htmlspecialchars($data['CustomerId']) . '</CustomerId>';
    $xml .= '<UserName>' . htmlspecialchars($data['UserName']) . '</UserName>';
    $xml .= '<TransactionType>' . htmlspecialchars($data['TransactionType']) . '</TransactionType>';
    $xml .= '<InstallmentCount>' . htmlspecialchars($data['InstallmentCount']) . '</InstallmentCount>';
    $xml .= '<Amount>' . htmlspecialchars($data['Amount']) . '</Amount>';
    $xml .= '<MerchantOrderId>' . htmlspecialchars($data['MerchantOrderId']) . '</MerchantOrderId>';
    $xml .= '<TransactionSecurity>3</TransactionSecurity>';
    $xml .= '<KuveytTurkVPosAdditionalData>';
    $xml .= '<AdditionalData>';
    $xml .= '<Key>MD</Key>';
    $xml .= '<Data>' . htmlspecialchars($data['BusinessKey']) . '</Data>';
    $xml .= '</AdditionalData>';
    $xml .= '</KuveytTurkVPosAdditionalData>';
    $xml .= '</KuveytTurkVPosMessage>';

    return $xml;
}

$fullTestData = array_merge($testData, [
    'CustomerId' => '98267952',
    'TransactionType' => 'Sale',
    'InstallmentCount' => '0',
    'BusinessKey' => '202508159551000000000024721'
]);

$xmlRequest = buildOfficialXml($fullTestData, $officialHash);
echo "✓ Official XML oluşturuldu (" . strlen($xmlRequest) . " karakter)\n";

// XML validation
$dom = new DOMDocument();
if ($dom->loadXML($xmlRequest)) {
    echo "✓ XML formatı geçerli\n";
    
    // HashData check
    $hashNodes = $dom->getElementsByTagName('HashData');
    if ($hashNodes->length > 0) {
        $xmlHash = $hashNodes->item(0)->textContent;
        if ($xmlHash === $officialHash) {
            echo "✓ XML'deki hash doğru\n";
        } else {
            echo "✗ XML'deki hash yanlış\n";
        }
    }
} else {
    echo "✗ XML formatı geçersiz\n";
}

// Test 4: Comparison with Documentation
echo "\nTest 4: Documentation Compliance\n";
echo "---------------------------------\n";

echo "Resmi Dokümandaki Format:\n";
echo "✓ Password önce hash'lendi\n";
echo "✓ Hash string: MerchantId + MerchantOrderId + Amount + UserName + HashedPassword\n";
echo "✓ Final hash: base64_encode(sha1(hashString, true))\n";
echo "✓ APIVersion: TDV2.0.0\n";
echo "✓ XML yapısı: Resmi dokümana uygun\n";

echo "\nBeklenen Sonuç:\n";
echo "✓ Base64 hatası alınmamalı\n";
echo "✓ Hash decrypt edilebilmeli\n";
echo "✓ Provision başarılı olmalı\n";
echo "✓ Para karttan çekilmeli\n";

echo "\n=== Test Tamamlandı ===\n";
echo "\nGerçek test için:\n";
echo "1. KuveytTurk POS ile ödeme yapın\n";
echo "2. Logları kontrol edin:\n";
echo "   tail -f storage/logs/laravel.log | grep -i 'official method'\n";
echo "\n3. Aranacak log mesajları:\n";
echo "   - 'KuveytPos provision hash calculated (official method)'\n";
echo "   - 'hashed_password' değeri\n";
echo "   - 'final_hash' değeri\n";
echo "\n4. Artık 'Invalid length for a Base-64 char array' hatası almamalısınız\n";
echo "5. KuveytTurk sunucusu hash'i decrypt edebilmeli\n";
echo "6. Provision başarılı olmalı ve para çekilmeli!\n";
