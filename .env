APP_NAME='Akdağ Yalıtım'
APP_ENV=local
APP_KEY=base64:/R+gtDEBq4ZbtTqsjAoINByA1hFy5RuNsblxbub5kt8=
APP_DEBUG=false
APP_URL=https://portal.akdagtasyunu.com

INITIAL_USERNAME=100000
DEMO_MODE=false
USER_REGISTRATION=false

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=ef_b2b
DB_USERNAME=ef_b2b
DB_PASSWORD=W1hDkvp6ofeq

APP_LOCALE=tr
APP_LC_ALL=tr_TR.UTF-8

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database
SESSION_DRIVER=redis
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

SESSION_SECURE_COOKIE=true
SESSION_SAME_SITE=none

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Akdağ Yalıtım"

MAIL_DRIVER=smtp
MAIL_HOST=mail.akdagtasyunu.com
MAIL_PORT=25
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD="9*[[mR1Rnaj;"
MAIL_ENCRYPTION=ssl

SCOUT_DRIVER=meilisearch
MEILISEARCH_HOST=http://meilisearch:7700

BACKUP_NOTIFICATION_EMAIL=<EMAIL>
SLACK_NOTIFICATION_WEBHOOK=

FACEBOOK_ACTIVE=true
FACEBOOK_CLIENT_ID=
FACEBOOK_CLIENT_SECRET=
FACEBOOK_REDIRECT=http://laravel-starter.local/login/facebook/callback

GITHUB_ACTIVE=true
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=
GITHUB_REDIRECT=http://laravel-starter.local/login/github/callback

GOOGLE_ACTIVE=true
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_REDIRECT=http://laravel-starter.local/login/google/callback

BKM_CLIENT_ID=62d2e4c4ee319775e1738043e744319b
BKM_CLIENT_SECRET=55e2235f1728734e699f38da60c8343f

OPENAI_API_KEY="********************************************************************************************************************************************************************"
